<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<title>Pong – Cyberpunk Neon Apocalypse</title>
<style>
    body {
        margin: 0;
        background:
            radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
            linear-gradient(45deg, #000000 0%, #001122 50%, #000000 100%);
        font-family: "Courier New", monospace;
        color: #0ff;
        overflow: hidden;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        animation: backgroundPulse 4s ease-in-out infinite alternate;
    }

    @keyframes backgroundPulse {
        0% { filter: brightness(1) contrast(1); }
        100% { filter: brightness(1.1) contrast(1.2); }
    }

    canvas {
        display: block;
        background:
            radial-gradient(circle at 15% 15%, rgba(0, 255, 0, 0.05) 0%, transparent 40%),
            radial-gradient(circle at 85% 85%, rgba(255, 0, 255, 0.05) 0%, transparent 40%),
            radial-gradient(circle at 50% 50%, rgba(0, 255, 255, 0.02) 0%, transparent 60%);
        border: 2px solid rgba(0, 255, 255, 0.3);
        box-shadow:
            0 0 20px rgba(0, 255, 255, 0.3),
            inset 0 0 20px rgba(0, 255, 255, 0.1);
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        touch-action: none; /* Prevent default touch behaviors */
        user-select: none; /* Prevent text selection */
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        -webkit-touch-callout: none; /* Prevent callout on iOS */
    }
    #score {
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 4em;
        font-weight: bold;
        text-shadow:
            0 0 10px #00ffff,
            0 0 20px #ff0080,
            0 0 30px #00ff00,
            0 0 40px #ffffff;
        color: #00ffff;
        z-index: 2;
        text-align: center;
        letter-spacing: 4px;
        animation: scorePulse 3s ease-in-out infinite;
        background: rgba(0, 0, 0, 0.3);
        padding: 10px 20px;
        border-radius: 10px;
        border: 1px solid rgba(0, 255, 255, 0.3);
    }

    #controls {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        color: rgba(0, 255, 255, 0.7);
        font-size: 0.9em;
        text-align: center;
        z-index: 2;
        background: rgba(0, 0, 0, 0.5);
        padding: 10px;
        border-radius: 5px;
        border: 1px solid rgba(0, 255, 255, 0.2);
    }

    #gameStatus {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2em;
        color: #ff0080;
        text-shadow: 0 0 20px #ff0080;
        z-index: 3;
        display: none;
        background: rgba(0, 0, 0, 0.8);
        padding: 20px;
        border-radius: 10px;
        border: 2px solid #ff0080;
        text-align: center;
    }

    @keyframes scorePulse {
        0% {
            text-shadow: 0 0 10px #00ffff, 0 0 20px #ff0080, 0 0 30px #00ff00, 0 0 40px #ffffff;
            transform: translateX(-50%) scale(1);
        }
        50% {
            text-shadow: 0 0 15px #00ffff, 0 0 25px #ff0080, 0 0 35px #00ff00, 0 0 45px #ffffff;
            transform: translateX(-50%) scale(1.05);
        }
        100% {
            text-shadow: 0 0 10px #00ffff, 0 0 20px #ff0080, 0 0 30px #00ff00, 0 0 40px #ffffff;
            transform: translateX(-50%) scale(1);
        }
    }

    /* Configuration Menu Styles */
    .config-menu {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        z-index: 1000;
        display: flex;
        justify-content: center;
        align-items: center;
        animation: fadeIn 0.3s ease-in-out;
    }

    .config-content {
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(20, 0, 40, 0.95));
        border: 2px solid #00ffff;
        border-radius: 15px;
        box-shadow: 0 0 30px #00ffff, inset 0 0 20px rgba(0, 255, 255, 0.1);
        padding: 20px;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        color: #ffffff;
        font-family: 'Courier New', monospace;
    }

    .config-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 1px solid #00ffff;
        padding-bottom: 10px;
    }

    .config-header h2 {
        color: #00ffff;
        text-shadow: 0 0 10px #00ffff;
        margin: 0;
        font-size: 1.5em;
    }

    .config-close {
        background: none;
        border: 2px solid #ff0080;
        color: #ff0080;
        font-size: 1.2em;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .config-close:hover {
        background: #ff0080;
        color: #000;
        box-shadow: 0 0 15px #ff0080;
    }

    .config-sections {
        display: grid;
        gap: 20px;
    }

    .config-section {
        background: rgba(0, 255, 255, 0.05);
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 10px;
        padding: 15px;
    }

    .config-section h3 {
        color: #ff0080;
        text-shadow: 0 0 8px #ff0080;
        margin: 0 0 15px 0;
        font-size: 1.2em;
    }

    .config-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding: 5px 0;
    }

    .config-item label {
        color: #00ffff;
        font-weight: bold;
        flex: 1;
    }

    .slider-container {
        display: flex;
        align-items: center;
        gap: 10px;
        flex: 1;
        justify-content: flex-end;
    }

    .config-slider {
        width: 150px;
        height: 5px;
        background: rgba(0, 255, 255, 0.3);
        border-radius: 5px;
        outline: none;
        -webkit-appearance: none;
        appearance: none;
    }

    .config-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 15px;
        height: 15px;
        background: #00ffff;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 0 10px #00ffff;
    }

    .config-slider::-moz-range-thumb {
        width: 15px;
        height: 15px;
        background: #00ffff;
        border-radius: 50%;
        cursor: pointer;
        border: none;
        box-shadow: 0 0 10px #00ffff;
    }

    .config-checkbox {
        width: 20px;
        height: 20px;
        accent-color: #00ffff;
        cursor: pointer;
    }

    .config-select {
        background: rgba(0, 0, 0, 0.7);
        border: 1px solid #00ffff;
        color: #00ffff;
        padding: 5px 10px;
        border-radius: 5px;
        font-family: 'Courier New', monospace;
        cursor: pointer;
    }

    .config-select option {
        background: rgba(0, 0, 0, 0.9);
        color: #00ffff;
    }

    .config-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #00ffff;
    }

    .config-button {
        padding: 10px 20px;
        border: 2px solid;
        border-radius: 8px;
        background: none;
        font-family: 'Courier New', monospace;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
    }

    .reset-button {
        border-color: #ff0080;
        color: #ff0080;
    }

    .reset-button:hover {
        background: #ff0080;
        color: #000;
        box-shadow: 0 0 15px #ff0080;
    }

    .apply-button {
        border-color: #00ffff;
        color: #00ffff;
    }

    .apply-button:hover {
        background: #00ffff;
        color: #000;
        box-shadow: 0 0 15px #00ffff;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.9); }
        to { opacity: 1; transform: scale(1); }
    }

    /* Scrollbar styling for config menu */
    .config-content::-webkit-scrollbar {
        width: 8px;
    }

    .config-content::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 4px;
    }

    .config-content::-webkit-scrollbar-thumb {
        background: #00ffff;
        border-radius: 4px;
        box-shadow: 0 0 5px #00ffff;
    }

    /* Upgrade Selection Screen Styles */
    .upgrade-selection {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.95);
        backdrop-filter: blur(15px);
        z-index: 2000;
        display: flex;
        justify-content: center;
        align-items: center;
        animation: upgradeSlideIn 0.5s ease-out;
    }

    .upgrade-content {
        background: linear-gradient(135deg, rgba(0, 30, 60, 0.98), rgba(30, 0, 60, 0.98));
        border: 3px solid #ff0080;
        border-radius: 20px;
        box-shadow: 0 0 50px #ff0080, inset 0 0 30px rgba(255, 0, 128, 0.1);
        padding: 30px;
        max-width: 800px;
        color: #ffffff;
        font-family: 'Courier New', monospace;
        text-align: center;
    }

    .upgrade-header h2 {
        color: #ff0080;
        text-shadow: 0 0 15px #ff0080;
        margin: 0 0 10px 0;
        font-size: 2em;
        animation: upgradePulse 2s infinite;
    }

    .upgrade-subtitle {
        color: #00ffff;
        font-size: 1.2em;
        margin-bottom: 30px;
        text-shadow: 0 0 8px #00ffff;
    }

    .upgrade-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .upgrade-option {
        background: rgba(0, 255, 255, 0.1);
        border: 2px solid rgba(0, 255, 255, 0.5);
        border-radius: 15px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .upgrade-option:hover {
        border-color: #00ffff;
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        transform: translateY(-5px);
    }

    .upgrade-option.selected {
        border-color: #ff0080;
        box-shadow: 0 0 25px rgba(255, 0, 128, 0.7);
        background: rgba(255, 0, 128, 0.2);
    }

    .upgrade-icon {
        font-size: 3em;
        margin-bottom: 10px;
        display: block;
        filter: drop-shadow(0 0 10px currentColor);
    }

    .upgrade-name {
        font-size: 1.3em;
        font-weight: bold;
        margin-bottom: 8px;
        color: #ffffff;
        text-shadow: 0 0 5px #ffffff;
    }

    .upgrade-description {
        font-size: 0.9em;
        color: #cccccc;
        line-height: 1.4;
        margin-bottom: 10px;
    }

    .upgrade-rarity {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        box-shadow: 0 0 8px currentColor;
    }

    .upgrade-stack-info {
        font-size: 0.8em;
        color: #ffff00;
        font-weight: bold;
        margin-top: 5px;
    }

    .upgrade-number {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(0, 255, 255, 0.8);
        color: #000;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9em;
    }

    .upgrade-instructions {
        color: #00ffff;
        font-size: 1em;
        text-shadow: 0 0 5px #00ffff;
    }

    @keyframes upgradeSlideIn {
        from {
            opacity: 0;
            transform: scale(0.8) translateY(50px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    @keyframes upgradePulse {
        0%, 100% {
            text-shadow: 0 0 15px #ff0080;
            transform: scale(1);
        }
        50% {
            text-shadow: 0 0 25px #ff0080, 0 0 35px #ff0080;
            transform: scale(1.05);
        }
    }

    .upgrade-option::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.3), transparent);
        border-radius: 15px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
    }

    .upgrade-option:hover::before {
        opacity: 1;
        animation: upgradeShimmer 2s infinite;
    }

    @keyframes upgradeShimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* Mobile-specific improvements */
    @media (max-width: 768px) {
        #controls {
            font-size: 0.8em;
            padding: 5px;
        }

        .upgrade-content {
            max-width: 95vw;
            padding: 20px;
        }

        .upgrade-options {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .config-content {
            max-width: 95vw;
            padding: 20px;
        }

        .config-section {
            margin-bottom: 20px;
        }
    }

    @media (max-height: 600px) {
        #score {
            font-size: 2.5em;
        }

        #controls {
            font-size: 0.7em;
        }
    }
</style>
</head>
<body>
<canvas id="c"></canvas>
<div id="score">0 : 0</div>
<div id="controls">
    Mouse/Touch or W/S Keys: Move Paddle | Spacebar: Pause/Resume | R: Restart | M: Music Toggle | +/-: Volume | C: Config Menu
</div>
<div id="gameStatus"></div>

<!-- Configuration Menu -->
<div id="configMenu" class="config-menu" style="display: none;">
    <div class="config-content">
        <div class="config-header">
            <h2>⚙️ CYBERPUNK PONG CONFIG ⚙️</h2>
            <button id="closeConfig" class="config-close">✕</button>
        </div>

        <div class="config-sections">
            <!-- Speed Configuration -->
            <div class="config-section">
                <h3>🚀 Speed Boost Settings</h3>
                <div class="config-item">
                    <label for="speedBoostSlider">Ball Speed Increase per Hit:</label>
                    <div class="slider-container">
                        <input type="range" id="speedBoostSlider" min="1" max="50" value="10" class="config-slider">
                        <span id="speedBoostValue">10%</span>
                    </div>
                </div>
            </div>

            <!-- Paddle Level-Up Configuration -->
            <div class="config-section">
                <h3>🔥 Paddle Level-Up System</h3>
                <div class="config-item">
                    <label for="paddleLevelToggle">Enable Paddle Level-Up:</label>
                    <input type="checkbox" id="paddleLevelToggle" checked class="config-checkbox">
                </div>
                <div class="config-item">
                    <label for="levelUpHitsSlider">Hits Required for Level-Up:</label>
                    <div class="slider-container">
                        <input type="range" id="levelUpHitsSlider" min="2" max="10" value="3" class="config-slider">
                        <span id="levelUpHitsValue">3</span>
                    </div>
                </div>
            </div>

            <!-- AI Difficulty Configuration -->
            <div class="config-section">
                <h3>🤖 AI Difficulty</h3>
                <div class="config-item">
                    <label for="aiDifficultySelect">Difficulty Preset:</label>
                    <select id="aiDifficultySelect" class="config-select">
                        <option value="easy">Easy</option>
                        <option value="medium" selected>Medium</option>
                        <option value="hard">Hard</option>
                        <option value="expert">Expert</option>
                    </select>
                </div>
                <div class="config-item">
                    <label for="aiSensitivitySlider">AI Sensitivity:</label>
                    <div class="slider-container">
                        <input type="range" id="aiSensitivitySlider" min="1" max="20" value="6" class="config-slider">
                        <span id="aiSensitivityValue">0.06</span>
                    </div>
                </div>
            </div>

            <!-- Audio Configuration -->
            <div class="config-section">
                <h3>🔊 Audio Settings</h3>
                <div class="config-item">
                    <label for="musicToggle">Background Music:</label>
                    <input type="checkbox" id="musicToggle" checked class="config-checkbox">
                </div>
                <div class="config-item">
                    <label for="musicVolumeSlider">Music Volume:</label>
                    <div class="slider-container">
                        <input type="range" id="musicVolumeSlider" min="0" max="100" value="30" class="config-slider">
                        <span id="musicVolumeValue">30%</span>
                    </div>
                </div>
                <div class="config-item">
                    <label for="soundVolumeSlider">Sound Effects Volume:</label>
                    <div class="slider-container">
                        <input type="range" id="soundVolumeSlider" min="0" max="100" value="100" class="config-slider">
                        <span id="soundVolumeValue">100%</span>
                    </div>
                </div>
            </div>

            <!-- Visual Configuration -->
            <div class="config-section">
                <h3>✨ Visual Effects</h3>
                <div class="config-item">
                    <label for="particleIntensitySelect">Particle Effects:</label>
                    <select id="particleIntensitySelect" class="config-select">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                        <option value="ultra">Ultra</option>
                    </select>
                </div>
                <div class="config-item">
                    <label for="visualEnhancementsToggle">Visual Enhancements:</label>
                    <input type="checkbox" id="visualEnhancementsToggle" checked class="config-checkbox">
                </div>
            </div>
        </div>

        <div class="config-footer">
            <button id="resetDefaults" class="config-button reset-button">Reset to Defaults</button>
            <button id="applyConfig" class="config-button apply-button">Apply Settings</button>
        </div>
    </div>
</div>

<!-- Upgrade Selection Screen -->
<div id="upgradeSelection" class="upgrade-selection" style="display: none;">
    <div class="upgrade-content">
        <div class="upgrade-header">
            <h2 id="upgradeTitle">🔥 PADDLE LEVEL UP! 🔥</h2>
            <div class="upgrade-subtitle">Choose your enhancement:</div>
        </div>

        <div class="upgrade-options" id="upgradeOptions">
            <!-- Upgrade options will be dynamically generated -->
        </div>

        <div class="upgrade-footer">
            <div class="upgrade-instructions">
                Click an upgrade or use number keys (1-4) to select
            </div>
        </div>
    </div>
</div>

<script>
(() => {
    const canvas = document.getElementById('c');
    const ctx = canvas.getContext('2d');
    const scoreEl = document.getElementById('score');

    // Responsive resize with game object repositioning
    function resize() {
        const oldWidth = canvas.width;
        const oldHeight = canvas.height;

        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        // Only reposition game objects if they exist and game was already initialized
        if (oldWidth > 0 && oldHeight > 0 && typeof paddles !== 'undefined' && typeof ball !== 'undefined' && typeof PADDLE_W !== 'undefined') {
            const widthRatio = canvas.width / oldWidth;
            const heightRatio = canvas.height / oldHeight;

            // Adjust paddle positions
            paddles.left.y = (paddles.left.y / oldHeight) * canvas.height;
            paddles.right.x = canvas.width - 30 - PADDLE_W;
            paddles.right.y = (paddles.right.y / oldHeight) * canvas.height;

            // Adjust ball position
            ball.x = (ball.x / oldWidth) * canvas.width;
            ball.y = (ball.y / oldHeight) * canvas.height;

            // Keep objects in bounds
            if (paddles.left.y + PADDLE_H > canvas.height) paddles.left.y = canvas.height - PADDLE_H;
            if (paddles.right.y + PADDLE_H > canvas.height) paddles.right.y = canvas.height - PADDLE_H;
            if (ball.y + BALL_S > canvas.height) ball.y = canvas.height - BALL_S;
        }
    }

    window.addEventListener('resize', resize);

    // Game configuration - now dynamic and configurable
    let CONFIG = {
        PADDLE_W: 12,
        PADDLE_H: 80,
        BALL_S: 12,
        MAX_SPEED: 8,
        AI_SENSITIVITY: 0.06,
        STREAK_THRESHOLD: 3,
        PARTICLE_COUNT: 20,
        GLITCH_INTERVAL: 15000, // 15 seconds
        MAX_PARTICLES: 150, // Increased for enhanced effects
        MAX_ENHANCEMENT_PARTICLES: 50, // Separate limit for enhancement particles
        PADDLE_SPEED: 6,
        BALL_SPEED_INCREMENT: 1.1, // Configurable speed increase
        PADDLE_LEVELUP_HITS: 3, // Configurable hits for level-up
        PADDLE_LEVELUP_ENABLED: true,
        SOUND_VOLUME: 1.0,
        PARTICLE_INTENSITY: 'medium',
        VISUAL_ENHANCEMENTS: true
    };

    // Configuration menu state
    let configMenuOpen = false;
    let configElements = {};

    // Configuration menu functions
    function initConfigMenu() {
        // Get all config elements
        configElements = {
            menu: document.getElementById('configMenu'),
            closeBtn: document.getElementById('closeConfig'),
            speedBoostSlider: document.getElementById('speedBoostSlider'),
            speedBoostValue: document.getElementById('speedBoostValue'),
            paddleLevelToggle: document.getElementById('paddleLevelToggle'),
            levelUpHitsSlider: document.getElementById('levelUpHitsSlider'),
            levelUpHitsValue: document.getElementById('levelUpHitsValue'),
            aiDifficultySelect: document.getElementById('aiDifficultySelect'),
            aiSensitivitySlider: document.getElementById('aiSensitivitySlider'),
            aiSensitivityValue: document.getElementById('aiSensitivityValue'),
            musicToggle: document.getElementById('musicToggle'),
            musicVolumeSlider: document.getElementById('musicVolumeSlider'),
            musicVolumeValue: document.getElementById('musicVolumeValue'),
            soundVolumeSlider: document.getElementById('soundVolumeSlider'),
            soundVolumeValue: document.getElementById('soundVolumeValue'),
            particleIntensitySelect: document.getElementById('particleIntensitySelect'),
            visualEnhancementsToggle: document.getElementById('visualEnhancementsToggle'),
            resetBtn: document.getElementById('resetDefaults'),
            applyBtn: document.getElementById('applyConfig')
        };

        // Add event listeners
        configElements.closeBtn.addEventListener('click', closeConfigMenu);
        configElements.resetBtn.addEventListener('click', resetToDefaults);
        configElements.applyBtn.addEventListener('click', applyConfiguration);

        // Slider event listeners with real-time updates
        configElements.speedBoostSlider.addEventListener('input', updateSpeedBoostDisplay);
        configElements.levelUpHitsSlider.addEventListener('input', updateLevelUpHitsDisplay);
        configElements.aiSensitivitySlider.addEventListener('input', updateAISensitivityDisplay);
        configElements.musicVolumeSlider.addEventListener('input', updateMusicVolumeDisplay);
        configElements.soundVolumeSlider.addEventListener('input', updateSoundVolumeDisplay);

        // AI difficulty preset handler
        configElements.aiDifficultySelect.addEventListener('change', handleAIDifficultyPreset);

        // Load saved configuration
        loadConfiguration();
        updateAllDisplays();
    }

    function openConfigMenu() {
        if (configMenuOpen) return;

        configMenuOpen = true;
        configElements.menu.style.display = 'flex';

        // Pause game if running
        if (gameRunning) {
            gameRunning = false;
            cancelAnimationFrame(animationId);
        }

        // Load current settings into UI
        syncUIWithConfig();
    }

    function closeConfigMenu() {
        if (!configMenuOpen) return;

        configMenuOpen = false;
        configElements.menu.style.display = 'none';

        // Resume game
        if (!gameWon) {
            gameRunning = true;
            gameLoop();
        }
    }

    function syncUIWithConfig() {
        configElements.speedBoostSlider.value = Math.round((CONFIG.BALL_SPEED_INCREMENT - 1) * 100);
        configElements.paddleLevelToggle.checked = CONFIG.PADDLE_LEVELUP_ENABLED;
        configElements.levelUpHitsSlider.value = CONFIG.PADDLE_LEVELUP_HITS;
        configElements.aiSensitivitySlider.value = Math.round(CONFIG.AI_SENSITIVITY * 100);
        configElements.musicToggle.checked = musicEnabled;
        configElements.musicVolumeSlider.value = Math.round(musicVolume * 100);
        configElements.soundVolumeSlider.value = Math.round(CONFIG.SOUND_VOLUME * 100);
        configElements.particleIntensitySelect.value = CONFIG.PARTICLE_INTENSITY;
        configElements.visualEnhancementsToggle.checked = CONFIG.VISUAL_ENHANCEMENTS;

        updateAllDisplays();
    }

    function updateAllDisplays() {
        updateSpeedBoostDisplay();
        updateLevelUpHitsDisplay();
        updateAISensitivityDisplay();
        updateMusicVolumeDisplay();
        updateSoundVolumeDisplay();
    }

    function updateSpeedBoostDisplay() {
        const value = configElements.speedBoostSlider.value;
        configElements.speedBoostValue.textContent = value + '%';
    }

    function updateLevelUpHitsDisplay() {
        const value = configElements.levelUpHitsSlider.value;
        configElements.levelUpHitsValue.textContent = value;
    }

    function updateAISensitivityDisplay() {
        const value = configElements.aiSensitivitySlider.value;
        configElements.aiSensitivityValue.textContent = (value / 100).toFixed(2);
    }

    function updateMusicVolumeDisplay() {
        const value = configElements.musicVolumeSlider.value;
        configElements.musicVolumeValue.textContent = value + '%';
    }

    function updateSoundVolumeDisplay() {
        const value = configElements.soundVolumeSlider.value;
        configElements.soundVolumeValue.textContent = value + '%';
    }

    function handleAIDifficultyPreset() {
        const preset = configElements.aiDifficultySelect.value;
        let sensitivity;

        switch (preset) {
            case 'easy': sensitivity = 0.03; break;
            case 'medium': sensitivity = 0.06; break;
            case 'hard': sensitivity = 0.10; break;
            case 'expert': sensitivity = 0.15; break;
            default: sensitivity = 0.06;
        }

        configElements.aiSensitivitySlider.value = Math.round(sensitivity * 100);
        updateAISensitivityDisplay();
    }

    function applyConfiguration() {
        // Apply speed boost setting
        CONFIG.BALL_SPEED_INCREMENT = 1 + (configElements.speedBoostSlider.value / 100);

        // Apply paddle level-up settings
        CONFIG.PADDLE_LEVELUP_ENABLED = configElements.paddleLevelToggle.checked;
        CONFIG.PADDLE_LEVELUP_HITS = parseInt(configElements.levelUpHitsSlider.value);

        // Apply AI settings
        CONFIG.AI_SENSITIVITY = configElements.aiSensitivitySlider.value / 100;

        // Apply audio settings
        musicEnabled = configElements.musicToggle.checked;
        musicVolume = configElements.musicVolumeSlider.value / 100;
        CONFIG.SOUND_VOLUME = configElements.soundVolumeSlider.value / 100;

        // Apply music volume immediately
        if (musicGainNode) {
            musicGainNode.gain.setValueAtTime(musicEnabled ? musicVolume : 0, audioContext.currentTime);
        }

        // Apply visual settings
        CONFIG.PARTICLE_INTENSITY = configElements.particleIntensitySelect.value;
        CONFIG.VISUAL_ENHANCEMENTS = configElements.visualEnhancementsToggle.checked;

        // Update particle limits based on intensity
        updateParticleLimits();

        // Save configuration
        saveConfiguration();

        // Visual feedback
        showGameStatus('⚙️ Configuration Applied Successfully! ⚙️', 2000);

        // Brief visual effect on apply button
        configElements.applyBtn.style.background = '#00ffff';
        configElements.applyBtn.style.color = '#000';
        setTimeout(() => {
            configElements.applyBtn.style.background = 'none';
            configElements.applyBtn.style.color = '#00ffff';
        }, 200);

        closeConfigMenu();
    }

    function resetToDefaults() {
        // Reset CONFIG to defaults
        CONFIG.BALL_SPEED_INCREMENT = 1.1;
        CONFIG.PADDLE_LEVELUP_ENABLED = true;
        CONFIG.PADDLE_LEVELUP_HITS = 3;
        CONFIG.AI_SENSITIVITY = 0.06;
        CONFIG.SOUND_VOLUME = 1.0;
        CONFIG.PARTICLE_INTENSITY = 'medium';
        CONFIG.VISUAL_ENHANCEMENTS = true;

        // Reset audio settings
        musicEnabled = true;
        musicVolume = 0.3;

        // Update UI
        syncUIWithConfig();

        // Clear saved settings
        localStorage.removeItem('cyberpongConfig');

        showGameStatus('Settings Reset to Defaults!', 1500);
    }

    function saveConfiguration() {
        const config = {
            ballSpeedIncrement: CONFIG.BALL_SPEED_INCREMENT,
            paddleLevelupEnabled: CONFIG.PADDLE_LEVELUP_ENABLED,
            paddleLevelupHits: CONFIG.PADDLE_LEVELUP_HITS,
            aiSensitivity: CONFIG.AI_SENSITIVITY,
            musicEnabled: musicEnabled,
            musicVolume: musicVolume,
            soundVolume: CONFIG.SOUND_VOLUME,
            particleIntensity: CONFIG.PARTICLE_INTENSITY,
            visualEnhancements: CONFIG.VISUAL_ENHANCEMENTS
        };

        try {
            localStorage.setItem('cyberpongConfig', JSON.stringify(config));
        } catch (error) {
            console.warn('Failed to save configuration:', error);
        }
    }

    function loadConfiguration() {
        try {
            const saved = localStorage.getItem('cyberpongConfig');
            if (saved) {
                const config = JSON.parse(saved);

                CONFIG.BALL_SPEED_INCREMENT = config.ballSpeedIncrement || 1.1;
                CONFIG.PADDLE_LEVELUP_ENABLED = config.paddleLevelupEnabled !== undefined ? config.paddleLevelupEnabled : true;
                CONFIG.PADDLE_LEVELUP_HITS = config.paddleLevelupHits || 3;
                CONFIG.AI_SENSITIVITY = config.aiSensitivity || 0.06;
                musicEnabled = config.musicEnabled !== undefined ? config.musicEnabled : true;
                musicVolume = config.musicVolume || 0.3;
                CONFIG.SOUND_VOLUME = config.soundVolume || 1.0;
                CONFIG.PARTICLE_INTENSITY = config.particleIntensity || 'medium';
                CONFIG.VISUAL_ENHANCEMENTS = config.visualEnhancements !== undefined ? config.visualEnhancements : true;

                updateParticleLimits();
            }
        } catch (error) {
            console.warn('Failed to load configuration:', error);
        }
    }

    function updateParticleLimits() {
        const intensityMap = {
            'low': { particles: 75, enhancement: 25 },
            'medium': { particles: 150, enhancement: 50 },
            'high': { particles: 250, enhancement: 75 },
            'ultra': { particles: 400, enhancement: 100 }
        };

        const limits = intensityMap[CONFIG.PARTICLE_INTENSITY] || intensityMap['medium'];
        CONFIG.MAX_PARTICLES = limits.particles;
        CONFIG.MAX_ENHANCEMENT_PARTICLES = limits.enhancement;
    }

    // Use constants from CONFIG
    const { PADDLE_W, PADDLE_H, BALL_S, MAX_SPEED, STREAK_THRESHOLD, PARTICLE_COUNT, GLITCH_INTERVAL } = CONFIG;

    // Game state
    let score = { left: 0, right: 0 };
    let leftStreak = 0;
    let rightStreak = 0;
    let lastGlitchTime = 0;
    let particles = [];
    let gameWon = false;
    const WINNING_SCORE = 7;

    // Progressive speed and visual enhancement system
    let consecutiveHits = 0;
    let ballSpeedMultiplier = 1.0;
    let visualEnhancementLevel = 0;
    let lastHitTime = 0;
    let enhancementParticles = [];
    let ballTrailIntensity = 0;

    // Paddle level-up system (permanent throughout game)
    let paddleLevels = { left: 0, right: 0 };
    let paddleConsecutiveHits = { left: 0, right: 0 };
    let lastHitPaddle = null;

    // Upgrade system
    let upgradeSelectionActive = false;
    let pendingUpgradePaddle = null;
    let paddleUpgrades = { left: [], right: [] };
    let activeUpgradeEffects = { left: {}, right: {} };

    // Upgrade definitions
    const UPGRADE_CATEGORIES = {
        PADDLE_MOD: 'paddle_mod',
        DEFENSIVE: 'defensive',
        OFFENSIVE: 'offensive',
        SPECIAL: 'special'
    };

    const UPGRADE_RARITY = {
        COMMON: { weight: 50, color: '#00ffff' },
        RARE: { weight: 30, color: '#ff0080' },
        EPIC: { weight: 15, color: '#ffff00' },
        LEGENDARY: { weight: 5, color: '#ff8000' }
    };

    const UPGRADE_DEFINITIONS = {
        // Paddle Modifications
        paddle_length: {
            id: 'paddle_length',
            name: 'Extended Paddle',
            description: 'Increases paddle length by 25%',
            category: UPGRADE_CATEGORIES.PADDLE_MOD,
            rarity: UPGRADE_RARITY.COMMON,
            icon: '📏',
            stackable: true,
            maxStacks: 3,
            effect: { lengthMultiplier: 1.25 }
        },
        paddle_width: {
            id: 'paddle_width',
            name: 'Thick Paddle',
            description: 'Increases paddle thickness for better ball control',
            category: UPGRADE_CATEGORIES.PADDLE_MOD,
            rarity: UPGRADE_RARITY.COMMON,
            icon: '🔲',
            stackable: true,
            maxStacks: 2,
            effect: { widthMultiplier: 1.5 }
        },
        paddle_speed: {
            id: 'paddle_speed',
            name: 'Speed Boost',
            description: 'Increases paddle movement speed by 50%',
            category: UPGRADE_CATEGORIES.PADDLE_MOD,
            rarity: UPGRADE_RARITY.RARE,
            icon: '⚡',
            stackable: true,
            maxStacks: 2,
            effect: { speedMultiplier: 1.5 }
        },
        magnetic_paddle: {
            id: 'magnetic_paddle',
            name: 'Magnetic Field',
            description: 'Slightly attracts ball when nearby',
            category: UPGRADE_CATEGORIES.PADDLE_MOD,
            rarity: UPGRADE_RARITY.EPIC,
            icon: '🧲',
            stackable: false,
            effect: { magneticRange: 50, magneticStrength: 0.3 }
        },

        // Defensive Abilities
        decel_zone: {
            id: 'decel_zone',
            name: 'Deceleration Field',
            description: 'Slows ball speed when approaching paddle',
            category: UPGRADE_CATEGORIES.DEFENSIVE,
            rarity: UPGRADE_RARITY.RARE,
            icon: '🛡️',
            stackable: true,
            maxStacks: 2,
            effect: { decelZoneRange: 80, decelStrength: 0.7 }
        },
        shield_barrier: {
            id: 'shield_barrier',
            name: 'Energy Shield',
            description: 'Brief invincibility after ball contact',
            category: UPGRADE_CATEGORIES.DEFENSIVE,
            rarity: UPGRADE_RARITY.EPIC,
            icon: '🔰',
            stackable: false,
            effect: { shieldDuration: 0.5 }
        },
        prediction_trail: {
            id: 'prediction_trail',
            name: 'Ball Tracker',
            description: 'Shows ball trajectory prediction',
            category: UPGRADE_CATEGORIES.DEFENSIVE,
            rarity: UPGRADE_RARITY.RARE,
            icon: '🎯',
            stackable: false,
            effect: { trailDuration: 1.0 }
        },
        auto_assist: {
            id: 'auto_assist',
            name: 'Auto-Positioning',
            description: 'Slight automatic movement toward ball',
            category: UPGRADE_CATEGORIES.DEFENSIVE,
            rarity: UPGRADE_RARITY.COMMON,
            icon: '🤖',
            stackable: true,
            maxStacks: 3,
            effect: { assistStrength: 0.2 }
        },

        // Offensive Abilities
        ball_accelerator: {
            id: 'ball_accelerator',
            name: 'Power Strike',
            description: 'Increases ball speed when hit',
            category: UPGRADE_CATEGORIES.OFFENSIVE,
            rarity: UPGRADE_RARITY.RARE,
            icon: '💥',
            stackable: true,
            maxStacks: 3,
            effect: { speedBoost: 1.3 }
        },
        multi_ball: {
            id: 'multi_ball',
            name: 'Ball Splitter',
            description: '20% chance to split ball into multiple balls',
            category: UPGRADE_CATEGORIES.OFFENSIVE,
            rarity: UPGRADE_RARITY.LEGENDARY,
            icon: '⚪',
            stackable: false,
            effect: { splitChance: 0.2, splitCount: 2, splitDuration: 3000 }
        },
        charged_shot: {
            id: 'charged_shot',
            name: 'Charge Attack',
            description: 'Hold to charge powerful shots',
            category: UPGRADE_CATEGORIES.OFFENSIVE,
            rarity: UPGRADE_RARITY.EPIC,
            icon: '⚡',
            stackable: false,
            effect: { maxCharge: 2.0, chargeRate: 0.02 }
        },
        curve_ball: {
            id: 'curve_ball',
            name: 'Spin Master',
            description: 'Adds curve to ball trajectory',
            category: UPGRADE_CATEGORIES.OFFENSIVE,
            rarity: UPGRADE_RARITY.RARE,
            icon: '🌀',
            stackable: true,
            maxStacks: 2,
            effect: { curveStrength: 0.5 }
        },

        // Special Abilities
        helper_paddle: {
            id: 'helper_paddle',
            name: 'AI Assistant',
            description: 'Small helper paddle follows main paddle',
            category: UPGRADE_CATEGORIES.SPECIAL,
            rarity: UPGRADE_RARITY.LEGENDARY,
            icon: '👥',
            stackable: false,
            effect: { helperSize: 0.4, helperOffset: 60 }
        },
        slow_motion: {
            id: 'slow_motion',
            name: 'Time Dilation',
            description: 'Slows time when ball approaches',
            category: UPGRADE_CATEGORIES.SPECIAL,
            rarity: UPGRADE_RARITY.EPIC,
            icon: '⏰',
            stackable: false,
            effect: { slowFactor: 0.5, triggerDistance: 100, duration: 1000 }
        },
        score_multiplier: {
            id: 'score_multiplier',
            name: 'Point Booster',
            description: '2x points for next 3 successful hits',
            category: UPGRADE_CATEGORIES.SPECIAL,
            rarity: UPGRADE_RARITY.RARE,
            icon: '💎',
            stackable: false,
            effect: { multiplier: 2, hitCount: 3 }
        },
        particle_shield: {
            id: 'particle_shield',
            name: 'Neon Barrier',
            description: 'Particle shield with minor deflection',
            category: UPGRADE_CATEGORIES.SPECIAL,
            rarity: UPGRADE_RARITY.COMMON,
            icon: '✨',
            stackable: true,
            maxStacks: 2,
            effect: { shieldRadius: 40, deflectionChance: 0.1 }
        }
    };

    // Upgrade system core functions
    function getAvailableUpgrades(paddleSide) {
        const currentUpgrades = paddleUpgrades[paddleSide];
        const available = [];

        for (const upgradeId in UPGRADE_DEFINITIONS) {
            const upgrade = UPGRADE_DEFINITIONS[upgradeId];
            const existing = currentUpgrades.find(u => u.id === upgradeId);

            // Check if upgrade can be added
            if (!existing) {
                available.push(upgrade);
            } else if (upgrade.stackable && existing.stacks < upgrade.maxStacks) {
                available.push({...upgrade, isStack: true, currentStacks: existing.stacks});
            }
        }

        return available;
    }

    function selectRandomUpgrades(paddleSide, count = 3) {
        const available = getAvailableUpgrades(paddleSide);
        const selected = [];

        // Weighted random selection based on rarity
        for (let i = 0; i < count && available.length > 0; i++) {
            const totalWeight = available.reduce((sum, upgrade) => sum + upgrade.rarity.weight, 0);
            let random = Math.random() * totalWeight;

            for (let j = 0; j < available.length; j++) {
                random -= available[j].rarity.weight;
                if (random <= 0) {
                    selected.push(available[j]);
                    available.splice(j, 1);
                    break;
                }
            }
        }

        return selected;
    }

    function applyUpgrade(paddleSide, upgradeId) {
        const upgrade = UPGRADE_DEFINITIONS[upgradeId];
        if (!upgrade) return;

        const currentUpgrades = paddleUpgrades[paddleSide];
        const existing = currentUpgrades.find(u => u.id === upgradeId);

        if (existing && upgrade.stackable) {
            existing.stacks = Math.min(existing.stacks + 1, upgrade.maxStacks);
        } else if (!existing) {
            currentUpgrades.push({
                id: upgradeId,
                stacks: 1,
                appliedAt: Date.now()
            });
        }

        // Update active effects
        updateActiveUpgradeEffects(paddleSide);

        // Visual and audio feedback
        createUpgradeAppliedEffect(paddleSide, upgrade);
        playCyberpunkSound('upgrade_applied', 1);

        console.log(`Applied upgrade ${upgrade.name} to ${paddleSide} paddle`);
    }

    function updateActiveUpgradeEffects(paddleSide) {
        const effects = {};
        const upgrades = paddleUpgrades[paddleSide];

        upgrades.forEach(upgrade => {
            const definition = UPGRADE_DEFINITIONS[upgrade.id];
            if (definition && definition.effect) {
                // Apply stacking effects
                Object.keys(definition.effect).forEach(key => {
                    const baseValue = definition.effect[key];
                    if (typeof baseValue === 'number') {
                        if (key.includes('Multiplier')) {
                            // Multiplicative stacking
                            effects[key] = (effects[key] || 1) * Math.pow(baseValue, upgrade.stacks);
                        } else {
                            // Additive stacking
                            effects[key] = (effects[key] || 0) + (baseValue * upgrade.stacks);
                        }
                    } else {
                        effects[key] = baseValue;
                    }
                });
            }
        });

        activeUpgradeEffects[paddleSide] = effects;
    }

    function createUpgradeAppliedEffect(paddleSide, upgrade) {
        const paddle = paddles[paddleSide];
        const centerX = paddle.x + PADDLE_W / 2;
        const centerY = paddle.y + PADDLE_H / 2;

        // Create upgrade notification particles
        for (let i = 0; i < 20; i++) {
            const angle = (Math.PI * 2 * i) / 20;
            const speed = 3 + Math.random() * 2;

            enhancementParticles.push({
                x: centerX + Math.cos(angle) * 30,
                y: centerY + Math.sin(angle) * 30,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                radius: 4,
                life: 1.5,
                decay: 0.015,
                color: upgrade.rarity.color,
                type: 'upgrade_applied'
            });
        }

        screenShake = 4;

        // Show upgrade notification
        showGameStatus(`${upgrade.icon} ${upgrade.name} Acquired!`, 2000);
    }

    // Upgrade selection UI functions
    function showUpgradeSelection(paddleSide) {
        if (upgradeSelectionActive) return;

        upgradeSelectionActive = true;
        pendingUpgradePaddle = paddleSide;

        // Pause game
        if (gameRunning) {
            gameRunning = false;
            cancelAnimationFrame(animationId);
        }

        // Get upgrade options
        const upgrades = selectRandomUpgrades(paddleSide, 3);

        // Update UI
        const titleEl = document.getElementById('upgradeTitle');
        const optionsEl = document.getElementById('upgradeOptions');
        const selectionEl = document.getElementById('upgradeSelection');

        titleEl.textContent = `🔥 ${paddleSide.toUpperCase()} PADDLE LEVEL UP! 🔥`;

        // Clear previous options
        optionsEl.innerHTML = '';

        // Create upgrade options
        upgrades.forEach((upgrade, index) => {
            const optionEl = document.createElement('div');
            optionEl.className = 'upgrade-option';
            optionEl.dataset.upgradeId = upgrade.id;
            optionEl.dataset.index = index;

            const stackInfo = upgrade.isStack ?
                `<div class="upgrade-stack-info">Stack ${upgrade.currentStacks + 1}/${upgrade.maxStacks}</div>` : '';

            optionEl.innerHTML = `
                <div class="upgrade-number">${index + 1}</div>
                <div class="upgrade-rarity" style="background-color: ${upgrade.rarity.color};"></div>
                <div class="upgrade-icon">${upgrade.icon}</div>
                <div class="upgrade-name">${upgrade.name}</div>
                <div class="upgrade-description">${upgrade.description}</div>
                ${stackInfo}
            `;

            // Add click and touch handlers
            optionEl.addEventListener('click', () => selectUpgrade(upgrade.id));
            optionEl.addEventListener('touchend', (e) => {
                e.preventDefault();
                // Haptic feedback for upgrade selection
                if (navigator.vibrate) {
                    navigator.vibrate(50); // Medium vibration for upgrade selection
                }
                selectUpgrade(upgrade.id);
            });

            optionsEl.appendChild(optionEl);
        });

        // Show selection screen
        selectionEl.style.display = 'flex';

        // Add keyboard handlers
        document.addEventListener('keydown', handleUpgradeKeydown);

        // Auto-select for AI after delay
        if (paddleSide === 'right') {
            setTimeout(() => {
                const selectedUpgrade = selectAIUpgrade(upgrades);
                selectUpgrade(selectedUpgrade.id);
            }, 1500); // Give player time to see AI options
        }
    }

    function selectUpgrade(upgradeId) {
        if (!upgradeSelectionActive || !pendingUpgradePaddle) return;

        // Apply the upgrade
        applyUpgrade(pendingUpgradePaddle, upgradeId);

        // Close selection screen
        closeUpgradeSelection();
    }

    function closeUpgradeSelection() {
        upgradeSelectionActive = false;
        pendingUpgradePaddle = null;

        // Hide selection screen
        document.getElementById('upgradeSelection').style.display = 'none';

        // Remove keyboard handlers
        document.removeEventListener('keydown', handleUpgradeKeydown);

        // Resume game
        if (!gameWon) {
            gameRunning = true;
            gameLoop();
        }
    }

    function handleUpgradeKeydown(e) {
        if (!upgradeSelectionActive) return;

        const key = e.key;
        if (key >= '1' && key <= '4') {
            e.preventDefault();
            const index = parseInt(key) - 1;
            const optionEl = document.querySelector(`[data-index="${index}"]`);
            if (optionEl) {
                const upgradeId = optionEl.dataset.upgradeId;
                selectUpgrade(upgradeId);
            }
        } else if (key === 'Escape') {
            e.preventDefault();
            // For player, allow escape to close (will skip upgrade)
            if (pendingUpgradePaddle === 'left') {
                closeUpgradeSelection();
            }
        }
    }

    function selectAIUpgrade(availableUpgrades) {
        // Enhanced AI upgrade selection logic based on comprehensive game state
        const scoreDiff = score.right - score.left;
        const isLosing = scoreDiff < -1;
        const isWinning = scoreDiff > 1;
        const currentLevel = paddleLevels.right;
        const ballSpeed = Math.sqrt(ball.vx * ball.vx + ball.vy * ball.vy);
        const gameProgress = (score.left + score.right) / 10; // Game progression factor

        // Analyze current AI upgrades
        const currentUpgrades = paddleUpgrades.right;
        const hasDefensive = currentUpgrades.some(u => UPGRADE_DEFINITIONS[u.id]?.category === UPGRADE_CATEGORIES.DEFENSIVE);
        const hasOffensive = currentUpgrades.some(u => UPGRADE_DEFINITIONS[u.id]?.category === UPGRADE_CATEGORIES.OFFENSIVE);
        const hasPaddleMod = currentUpgrades.some(u => UPGRADE_DEFINITIONS[u.id]?.category === UPGRADE_CATEGORIES.PADDLE_MOD);

        // Weight upgrades based on sophisticated strategy
        const weightedUpgrades = availableUpgrades.map(upgrade => {
            let weight = upgrade.rarity.weight;
            const category = upgrade.category;

            // Strategic weighting based on game state
            if (isLosing) {
                // Desperate measures when losing
                if (category === UPGRADE_CATEGORIES.DEFENSIVE) weight *= 3;
                if (category === UPGRADE_CATEGORIES.PADDLE_MOD) weight *= 2;
                if (upgrade.id === 'paddle_length' || upgrade.id === 'paddle_speed') weight *= 2.5;
                if (upgrade.id === 'decel_zone' || upgrade.id === 'shield_barrier') weight *= 3;
            } else if (isWinning) {
                // Aggressive play when winning
                if (category === UPGRADE_CATEGORIES.OFFENSIVE) weight *= 2.5;
                if (category === UPGRADE_CATEGORIES.SPECIAL) weight *= 2;
                if (upgrade.id === 'ball_accelerator' || upgrade.id === 'multi_ball') weight *= 3;
                if (upgrade.id === 'score_multiplier') weight *= 2.5;
            } else {
                // Balanced approach when tied - build foundation
                if (category === UPGRADE_CATEGORIES.PADDLE_MOD) weight *= 1.8;
                if (upgrade.id === 'paddle_length' && !hasPaddleMod) weight *= 2.5; // Prioritize basic upgrades early
            }

            // Level-based strategy
            if (currentLevel < 3) {
                // Early game - focus on fundamentals
                if (category === UPGRADE_CATEGORIES.PADDLE_MOD) weight *= 1.5;
                if (upgrade.id === 'paddle_length' || upgrade.id === 'paddle_speed') weight *= 2;
            } else {
                // Late game - focus on advanced abilities
                if (category === UPGRADE_CATEGORIES.SPECIAL) weight *= 1.5;
                if (upgrade.id === 'helper_paddle' || upgrade.id === 'slow_motion') weight *= 2;
            }

            // Ball speed adaptation
            if (ballSpeed > 8) {
                // High speed game - need defensive measures
                if (upgrade.id === 'decel_zone' || upgrade.id === 'prediction_trail') weight *= 2;
                if (upgrade.id === 'auto_assist') weight *= 1.8;
            }

            // Avoid redundancy - don't stack too much of same category
            if (category === UPGRADE_CATEGORIES.DEFENSIVE && hasDefensive) weight *= 0.7;
            if (category === UPGRADE_CATEGORIES.OFFENSIVE && hasOffensive) weight *= 0.8;

            // Game progression factor - more aggressive as game goes on
            if (gameProgress > 0.5) {
                if (category === UPGRADE_CATEGORIES.OFFENSIVE || category === UPGRADE_CATEGORIES.SPECIAL) {
                    weight *= (1 + gameProgress);
                }
            }

            // Specific upgrade preferences
            switch (upgrade.id) {
                case 'magnetic_paddle':
                    weight *= isLosing ? 2 : 1.2; // Great for defense
                    break;
                case 'multi_ball':
                    weight *= isWinning ? 3 : 0.5; // Only when ahead
                    break;
                case 'helper_paddle':
                    weight *= currentLevel >= 3 ? 2 : 0.8; // Late game upgrade
                    break;
                case 'score_multiplier':
                    weight *= isWinning ? 2.5 : 1; // Capitalize on advantage
                    break;
                case 'charged_shot':
                    weight *= 0.5; // AI doesn't use charged shots effectively
                    break;
            }

            return { upgrade, weight };
        });

        // Select based on weighted probability with some randomness
        const totalWeight = weightedUpgrades.reduce((sum, item) => sum + item.weight, 0);
        let random = Math.random() * totalWeight;

        for (const item of weightedUpgrades) {
            random -= item.weight;
            if (random <= 0) {
                console.log(`AI selected upgrade: ${item.upgrade.name} (weight: ${item.weight.toFixed(1)})`);
                return item.upgrade;
            }
        }

        return availableUpgrades[0]; // Fallback
    }

    // Upgrade effect implementations
    function applyMagneticEffects() {
        // Check both paddles for magnetic effects
        ['left', 'right'].forEach(paddleSide => {
            const effects = activeUpgradeEffects[paddleSide];
            if (effects.magneticRange && effects.magneticStrength) {
                const paddle = paddles[paddleSide];
                const paddleCenterX = paddle.x + PADDLE_W / 2;
                const paddleCenterY = paddle.y + PADDLE_H / 2;

                const ballCenterX = ball.x + BALL_S / 2;
                const ballCenterY = ball.y + BALL_S / 2;

                const distance = Math.sqrt(
                    Math.pow(ballCenterX - paddleCenterX, 2) +
                    Math.pow(ballCenterY - paddleCenterY, 2)
                );

                if (distance < effects.magneticRange) {
                    const force = effects.magneticStrength * (1 - distance / effects.magneticRange);
                    const angle = Math.atan2(paddleCenterY - ballCenterY, paddleCenterX - ballCenterX);

                    ball.vx += Math.cos(angle) * force;
                    ball.vy += Math.sin(angle) * force;

                    // Visual effect for magnetic field
                    if (Math.random() < 0.3) {
                        enhancementParticles.push({
                            x: ballCenterX + (Math.random() - 0.5) * 20,
                            y: ballCenterY + (Math.random() - 0.5) * 20,
                            vx: Math.cos(angle) * 2,
                            vy: Math.sin(angle) * 2,
                            radius: 2,
                            life: 0.5,
                            decay: 0.02,
                            color: 'rgba(255, 255, 0, 0.7)',
                            type: 'magnetic'
                        });
                    }
                }
            }
        });
    }

    function applyDecelZoneEffects() {
        ['left', 'right'].forEach(paddleSide => {
            const effects = activeUpgradeEffects[paddleSide];
            if (effects.decelZoneRange && effects.decelStrength) {
                const paddle = paddles[paddleSide];
                const paddleCenterX = paddle.x + PADDLE_W / 2;
                const paddleCenterY = paddle.y + PADDLE_H / 2;

                const ballCenterX = ball.x + BALL_S / 2;
                const ballCenterY = ball.y + BALL_S / 2;

                const distance = Math.sqrt(
                    Math.pow(ballCenterX - paddleCenterX, 2) +
                    Math.pow(ballCenterY - paddleCenterY, 2)
                );

                if (distance < effects.decelZoneRange) {
                    const decelFactor = effects.decelStrength + (1 - effects.decelStrength) * (distance / effects.decelZoneRange);
                    ball.vx *= decelFactor;
                    ball.vy *= decelFactor;

                    // Visual effect for decel zone
                    if (Math.random() < 0.2) {
                        enhancementParticles.push({
                            x: ballCenterX + (Math.random() - 0.5) * 30,
                            y: ballCenterY + (Math.random() - 0.5) * 30,
                            vx: (Math.random() - 0.5) * 2,
                            vy: (Math.random() - 0.5) * 2,
                            radius: 3,
                            life: 0.8,
                            decay: 0.015,
                            color: 'rgba(0, 255, 255, 0.6)',
                            type: 'decel_zone'
                        });
                    }
                }
            }
        });
    }

    function applyAutoAssist() {
        const effects = activeUpgradeEffects.left; // Only for player
        if (effects.assistStrength) {
            const ballCenterY = ball.y + BALL_S / 2;
            const paddleCenterY = paddles.left.y + PADDLE_H / 2;
            const diff = ballCenterY - paddleCenterY;

            if (Math.abs(diff) > 10) { // Only assist if ball is not centered
                const assistMove = Math.sign(diff) * effects.assistStrength * CONFIG.PADDLE_SPEED;
                paddles.left.y += assistMove;

                // Keep paddle in bounds
                paddles.left.y = Math.max(0, Math.min(canvas.height - PADDLE_H, paddles.left.y));
            }
        }
    }

    function drawPredictionTrail() {
        ['left', 'right'].forEach(paddleSide => {
            const effects = activeUpgradeEffects[paddleSide];
            if (effects.trailDuration) {
                const paddle = paddles[paddleSide];
                const isLeftPaddle = paddleSide === 'left';

                // Only show prediction when ball is moving toward this paddle
                if ((isLeftPaddle && ball.vx < 0) || (!isLeftPaddle && ball.vx > 0)) {
                    // Simple trajectory prediction (ignoring wall bounces for now)
                    const steps = Math.floor(effects.trailDuration * 60); // 60 FPS
                    let predX = ball.x;
                    let predY = ball.y;
                    let predVx = ball.vx;
                    let predVy = ball.vy;

                    ctx.save();
                    ctx.strokeStyle = `rgba(255, 255, 0, 0.5)`;
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(predX + BALL_S/2, predY + BALL_S/2);

                    for (let i = 0; i < steps && i < 100; i++) {
                        predX += predVx;
                        predY += predVy;

                        // Simple wall bounce prediction
                        if (predY <= 0 || predY >= canvas.height - BALL_S) {
                            predVy = -predVy;
                        }

                        ctx.lineTo(predX + BALL_S/2, predY + BALL_S/2);

                        // Stop prediction at paddle area
                        if ((isLeftPaddle && predX <= paddle.x + PADDLE_W) ||
                            (!isLeftPaddle && predX >= paddle.x)) {
                            break;
                        }
                    }

                    ctx.stroke();
                    ctx.restore();
                }
            }
        });
    }

    // Multi-ball system
    let multiBalls = [];

    function createMultiBall(effects) {
        const splitCount = effects.splitCount || 2;
        const duration = effects.splitDuration || 3000;

        for (let i = 0; i < splitCount; i++) {
            const angle = (Math.PI * 2 * i) / splitCount;
            const speed = Math.sqrt(ball.vx * ball.vx + ball.vy * ball.vy) * 0.8;

            multiBalls.push({
                x: ball.x,
                y: ball.y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                radius: BALL_S * 0.7,
                life: duration,
                curve: 0,
                createdAt: Date.now()
            });
        }

        // Visual effect
        createUpgradeAppliedEffect(leftHit ? 'left' : 'right', {
            name: 'Multi-Ball!',
            icon: '⚪',
            rarity: { color: '#ff8000' }
        });

        playCyberpunkSound('upgrade_applied', 1);
    }

    function updateMultiBalls() {
        for (let i = multiBalls.length - 1; i >= 0; i--) {
            const multiBall = multiBalls[i];

            // Check if expired
            if (Date.now() - multiBall.createdAt > multiBall.life) {
                multiBalls.splice(i, 1);
                continue;
            }

            // Apply curve effect
            if (multiBall.curve) {
                multiBall.vy += multiBall.curve;
                multiBall.curve *= 0.98; // Decay curve over time
            }

            // Update position
            multiBall.x += multiBall.vx;
            multiBall.y += multiBall.vy;

            // Wall collisions
            if (multiBall.y <= 0 || multiBall.y >= canvas.height - multiBall.radius) {
                multiBall.vy = -multiBall.vy;
                multiBall.y = Math.max(0, Math.min(canvas.height - multiBall.radius, multiBall.y));
            }

            // Paddle collisions (simplified)
            if ((multiBall.x <= paddles.left.x + PADDLE_W && multiBall.vx < 0) ||
                (multiBall.x >= paddles.right.x && multiBall.vx > 0)) {
                multiBall.vx = -multiBall.vx;
            }

            // Remove if off screen
            if (multiBall.x < -50 || multiBall.x > canvas.width + 50) {
                multiBalls.splice(i, 1);
            }
        }
    }

    function drawMultiBalls() {
        multiBalls.forEach(multiBall => {
            ctx.save();
            ctx.shadowBlur = 10;
            ctx.shadowColor = '#ff8000';
            ctx.fillStyle = '#ff8000';
            ctx.beginPath();
            ctx.arc(multiBall.x + multiBall.radius/2, multiBall.y + multiBall.radius/2,
                   multiBall.radius/2, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        });
    }

    // Special abilities
    let helperPaddles = { left: null, right: null };
    let slowMotionActive = false;
    let slowMotionEndTime = 0;
    let scoreMultipliers = { left: { active: false, multiplier: 1, hitsLeft: 0 },
                            right: { active: false, multiplier: 1, hitsLeft: 0 } };

    function updateHelperPaddles() {
        ['left', 'right'].forEach(paddleSide => {
            const effects = activeUpgradeEffects[paddleSide];
            if (effects.helperSize && effects.helperOffset) {
                const mainPaddle = paddles[paddleSide];

                if (!helperPaddles[paddleSide]) {
                    helperPaddles[paddleSide] = {
                        x: mainPaddle.x,
                        y: mainPaddle.y + effects.helperOffset,
                        size: effects.helperSize
                    };
                }

                // Update helper paddle position
                const helper = helperPaddles[paddleSide];
                const targetY = mainPaddle.y + effects.helperOffset;
                helper.y += (targetY - helper.y) * 0.1; // Smooth following
                helper.x = mainPaddle.x;

                // Keep helper in bounds
                helper.y = Math.max(0, Math.min(canvas.height - PADDLE_H * effects.helperSize, helper.y));
            } else {
                helperPaddles[paddleSide] = null;
            }
        });
    }

    function drawHelperPaddles() {
        ['left', 'right'].forEach(paddleSide => {
            const helper = helperPaddles[paddleSide];
            if (helper) {
                const effects = activeUpgradeEffects[paddleSide];
                ctx.save();
                ctx.globalAlpha = 0.7;
                ctx.shadowBlur = 8;
                ctx.shadowColor = '#00ffff';
                ctx.fillStyle = '#00ffff';
                ctx.fillRect(helper.x, helper.y, PADDLE_W * helper.size, PADDLE_H * helper.size);
                ctx.restore();
            }
        });
    }

    function checkSlowMotion() {
        ['left', 'right'].forEach(paddleSide => {
            const effects = activeUpgradeEffects[paddleSide];
            if (effects.slowFactor && effects.triggerDistance && effects.duration) {
                const paddle = paddles[paddleSide];
                const paddleCenterX = paddle.x + PADDLE_W / 2;
                const ballCenterX = ball.x + BALL_S / 2;

                const distance = Math.abs(ballCenterX - paddleCenterX);

                if (distance < effects.triggerDistance && !slowMotionActive) {
                    slowMotionActive = true;
                    slowMotionEndTime = Date.now() + effects.duration;

                    // Visual effect
                    createUpgradeAppliedEffect(paddleSide, {
                        name: 'Time Dilation!',
                        icon: '⏰',
                        rarity: { color: '#ffff00' }
                    });
                }
            }
        });

        // Check if slow motion should end
        if (slowMotionActive && Date.now() > slowMotionEndTime) {
            slowMotionActive = false;
        }
    }

    function updateScoreMultipliers() {
        ['left', 'right'].forEach(paddleSide => {
            const multiplier = scoreMultipliers[paddleSide];
            if (multiplier.active && multiplier.hitsLeft <= 0) {
                multiplier.active = false;
                multiplier.multiplier = 1;
            }
        });
    }

    function activateScoreMultiplier(paddleSide) {
        const effects = activeUpgradeEffects[paddleSide];
        if (effects.multiplier && effects.hitCount) {
            const multiplier = scoreMultipliers[paddleSide];
            multiplier.active = true;
            multiplier.multiplier = effects.multiplier;
            multiplier.hitsLeft = effects.hitCount;

            showGameStatus(`${paddleSide.toUpperCase()} ${effects.multiplier}x POINTS!`, 1500);
        }
    }

    // Paddles - Initialize after canvas is sized
    let paddles = {
        left: { x: 30, y: 0 },
        right: { x: 0, y: 0 }
    };

    // Ball - Initialize after canvas is sized
    let ball = {
        x: 0,
        y: 0,
        vx: 0,
        vy: 0,
        curve: 0
    };

    // Initialize game objects after canvas is properly sized
    function initializeGameObjects() {
        // Set paddle positions
        paddles.left.y = canvas.height / 2 - PADDLE_H / 2;
        paddles.right.x = canvas.width - 30 - PADDLE_W;
        paddles.right.y = canvas.height / 2 - PADDLE_H / 2;

        // Reset ball to center
        resetBall();
    }

    // Initialize particles
    function initParticles() {
        particles = [];
        for (let i = 0; i < PARTICLE_COUNT; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                radius: Math.random() * 2 + 1,
                speedX: (Math.random() - 0.5) * 2,
                speedY: (Math.random() - 0.5) * 2,
                color: `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 0.5})`
            });
        }
    }

    // Input handling
    const keys = {};

    // Mouse control
    canvas.addEventListener('mousemove', e => {
        const rect = canvas.getBoundingClientRect();

        // Account for upgraded paddle size
        const leftEffects = activeUpgradeEffects.left;
        const lengthMultiplier = leftEffects.lengthMultiplier || 1;
        const currentPaddleH = PADDLE_H * lengthMultiplier;

        paddles.left.y = e.clientY - rect.top - currentPaddleH / 2;
        if (paddles.left.y < 0) paddles.left.y = 0;
        if (paddles.left.y + currentPaddleH > canvas.height) paddles.left.y = canvas.height - currentPaddleH;
    });

    // Touch control for mobile devices
    function handleTouchMove(e) {
        e.preventDefault(); // Prevent scrolling
        const rect = canvas.getBoundingClientRect();
        const touch = e.touches[0];
        if (touch) {
            // Account for upgraded paddle size
            const leftEffects = activeUpgradeEffects.left;
            const lengthMultiplier = leftEffects.lengthMultiplier || 1;
            const currentPaddleH = PADDLE_H * lengthMultiplier;

            paddles.left.y = touch.clientY - rect.top - currentPaddleH / 2;
            if (paddles.left.y < 0) paddles.left.y = 0;
            if (paddles.left.y + currentPaddleH > canvas.height) paddles.left.y = canvas.height - currentPaddleH;
        }
    }

    // Touch indicator for visual feedback
    let touchIndicator = { active: false, x: 0, y: 0, fadeTime: 0 };

    // Touch event listeners
    canvas.addEventListener('touchstart', e => {
        e.preventDefault();
        handleTouchMove(e);

        // Haptic feedback if available
        if (navigator.vibrate) {
            navigator.vibrate(10); // Very short vibration
        }

        // Show touch indicator
        const rect = canvas.getBoundingClientRect();
        const touch = e.touches[0];
        if (touch) {
            touchIndicator.active = true;
            touchIndicator.x = touch.clientX - rect.left;
            touchIndicator.y = touch.clientY - rect.top;
            touchIndicator.fadeTime = Date.now() + 1000; // Show for 1 second
        }
    }, { passive: false });

    canvas.addEventListener('touchmove', e => {
        handleTouchMove(e);

        // Update touch indicator position
        const rect = canvas.getBoundingClientRect();
        const touch = e.touches[0];
        if (touch) {
            touchIndicator.x = touch.clientX - rect.left;
            touchIndicator.y = touch.clientY - rect.top;
            touchIndicator.fadeTime = Date.now() + 500; // Extend fade time
        }
    }, { passive: false });

    canvas.addEventListener('touchend', e => {
        e.preventDefault();
        // Let touch indicator fade out naturally
    }, { passive: false });

    // Keyboard controls
    document.addEventListener('keydown', e => {
        keys[e.key.toLowerCase()] = true;

        // Pause/unpause with spacebar
        if (e.key === ' ') {
            e.preventDefault();
            if (!gameWon) {
                togglePause();
            }
        }

        // Restart game with R key
        if (e.key.toLowerCase() === 'r') {
            e.preventDefault();
            restartGame();
        }

        // Music volume controls
        if (e.key === '+' || e.key === '=') {
            e.preventDefault();
            adjustMusicVolume(0.1);
        }
        if (e.key === '-' || e.key === '_') {
            e.preventDefault();
            adjustMusicVolume(-0.1);
        }
        if (e.key.toLowerCase() === 'm') {
            e.preventDefault();
            toggleMusic();
        }

        // Configuration menu with C key
        if (e.key.toLowerCase() === 'c') {
            e.preventDefault();
            if (configMenuOpen) {
                closeConfigMenu();
            } else {
                openConfigMenu();
            }
        }

        // ESC key to close config menu
        if (e.key === 'Escape' && configMenuOpen) {
            e.preventDefault();
            closeConfigMenu();
        }
    });

    document.addEventListener('keyup', e => {
        keys[e.key.toLowerCase()] = false;
    });

    // Handle keyboard paddle movement with bounds checking and upgrades
    function handleKeyboardInput() {
        try {
            const effects = activeUpgradeEffects.left;
            const speedMultiplier = effects.speedMultiplier || 1;
            const paddleSpeed = CONFIG.PADDLE_SPEED * speedMultiplier;

            if (keys['w'] || keys['arrowup']) {
                paddles.left.y = Math.max(0, paddles.left.y - paddleSpeed);
            }
            if (keys['s'] || keys['arrowdown']) {
                paddles.left.y = Math.min(canvas.height - PADDLE_H, paddles.left.y + paddleSpeed);
            }
        } catch (error) {
            console.warn('Error in keyboard input handling:', error);
        }
    }

    // Game status display
    function showGameStatus(message, duration = 2000) {
        if (!gameStatusEl) {
            gameStatusEl = document.getElementById('gameStatus');
        }

        gameStatusEl.textContent = message;
        gameStatusEl.style.display = 'block';

        setTimeout(() => {
            gameStatusEl.style.display = 'none';
        }, duration);
    }

    // Pause/unpause functionality
    function togglePause() {
        if (gameWon) return; // Don't allow pause when game is won

        gameRunning = !gameRunning;
        if (gameRunning) {
            showGameStatus('Game Resumed', 1000);
            // Resume audio context if needed
            if (audioContext && audioContext.state === 'suspended') {
                audioContext.resume();
            }
            gameLoop();
        } else {
            cancelAnimationFrame(animationId);
            showGameStatus('Game Paused - Press SPACE to Resume', 0);
        }
    }

    // Paddle level-up system management
    function updatePaddleLevels(hitPaddle) {
        // Skip if paddle level-up system is disabled
        if (!CONFIG.PADDLE_LEVELUP_ENABLED) return;

        // Reset consecutive hits if different paddle was hit
        if (lastHitPaddle && lastHitPaddle !== hitPaddle) {
            paddleConsecutiveHits.left = 0;
            paddleConsecutiveHits.right = 0;
        }

        // Increment consecutive hits for the paddle that was hit
        paddleConsecutiveHits[hitPaddle]++;
        lastHitPaddle = hitPaddle;

        // Level up based on configured hits required
        if (paddleConsecutiveHits[hitPaddle] % CONFIG.PADDLE_LEVELUP_HITS === 0) {
            paddleLevels[hitPaddle]++;
            createPaddleLevelUpEffect(hitPaddle);
            playCyberpunkSound('paddle_levelup', paddleLevels[hitPaddle]);

            // Show upgrade selection screen
            setTimeout(() => {
                showUpgradeSelection(hitPaddle);
            }, 500); // Brief delay for level-up effect
        }
    }

    // Create level-up effect for paddle
    function createPaddleLevelUpEffect(paddleSide) {
        const paddle = paddles[paddleSide];
        const centerX = paddle.x + PADDLE_W / 2;
        const centerY = paddle.y + PADDLE_H / 2;

        // Create energy burst around paddle
        for (let i = 0; i < 12; i++) {
            const angle = (Math.PI * 2 * i) / 12;
            const speed = 3 + Math.random() * 2;

            enhancementParticles.push({
                x: centerX + Math.cos(angle) * 20,
                y: centerY + Math.sin(angle) * 20,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                radius: 4,
                life: 1.0,
                decay: 0.02,
                color: paddleSide === 'left' ? 'rgba(0, 255, 255, 0.9)' : 'rgba(255, 0, 128, 0.9)',
                type: 'paddle_levelup'
            });
        }

        screenShake = 2;
    }

    // Restart game functionality
    function restartGame() {
        // Reset all game state
        score = { left: 0, right: 0 };
        leftStreak = 0;
        rightStreak = 0;
        gameWon = false;
        gameRunning = true;
        particles = [];
        enhancementParticles = [];
        lastGlitchTime = Date.now();

        // Reset progressive system
        resetProgressiveSystem();

        // Reset paddle levels (permanent until restart)
        paddleLevels = { left: 0, right: 0 };
        paddleConsecutiveHits = { left: 0, right: 0 };
        lastHitPaddle = null;

        // Reinitialize game objects
        initializeGameObjects();
        initParticles();

        // Hide game status and restart
        if (gameStatusEl) {
            gameStatusEl.style.display = 'none';
        }

        showGameStatus('Game Restarted!', 1500);
        gameLoop();
    }

    // Game state management
    let gameRunning = true;
    let animationId;
    let gameStatusEl = null;

    // Performance monitoring
    let frameCount = 0;
    let lastFpsTime = Date.now();
    let currentFps = 60;

    // Audio context for sound effects and music (Web Audio API)
    let audioContext = null;
    let soundEnabled = true;
    let musicEnabled = true;
    let musicVolume = 0.3;
    let musicGainNode = null;
    let currentMusicNodes = [];
    let musicPattern = 0;
    let musicBeat = 0;
    let audioInitialized = false;
    let musicStarted = false;

    // Initialize audio
    function initAudio() {
        try {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();

            // Create master gain node for music
            musicGainNode = audioContext.createGain();
            musicGainNode.connect(audioContext.destination);
            musicGainNode.gain.setValueAtTime(musicVolume, audioContext.currentTime);

            console.log('Audio context initialized successfully');
        } catch (e) {
            console.log('Web Audio API not supported');
            soundEnabled = false;
            musicEnabled = false;
        }
    }

    // Cyberpunk music scales and patterns
    const MUSIC_SCALES = {
        cyberpunk: [220, 246.94, 277.18, 311.13, 349.23, 392.00, 440.00, 493.88], // A minor pentatonic + variations
        bass: [110, 123.47, 138.59, 155.56, 174.61, 196.00, 220.00, 246.94],
        lead: [440, 493.88, 554.37, 622.25, 698.46, 783.99, 880.00, 987.77]
    };

    const DRUM_PATTERNS = [
        [1, 0, 1, 0, 1, 0, 1, 0], // Basic 4/4
        [1, 0, 0, 1, 1, 0, 0, 1], // Syncopated
        [1, 1, 0, 1, 0, 1, 1, 0], // Complex
    ];

    // Generate procedural 8-bit cyberpunk music
    function createMusicNote(frequency, startTime, duration, type = 'square', volume = 0.1) {
        if (!musicEnabled || !audioContext || !musicGainNode) return null;

        try {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            const filterNode = audioContext.createBiquadFilter();

            // 8-bit style square wave
            oscillator.type = type;
            oscillator.frequency.setValueAtTime(frequency, startTime);

            // Add some filtering for cyberpunk feel
            filterNode.type = 'lowpass';
            filterNode.frequency.setValueAtTime(frequency * 2, startTime);
            filterNode.Q.setValueAtTime(1, startTime);

            // Connect nodes
            oscillator.connect(filterNode);
            filterNode.connect(gainNode);
            gainNode.connect(musicGainNode);

            // Envelope for 8-bit feel
            gainNode.gain.setValueAtTime(0, startTime);
            gainNode.gain.linearRampToValueAtTime(volume, startTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(volume * 0.7, startTime + duration * 0.7);
            gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

            oscillator.start(startTime);
            oscillator.stop(startTime + duration);

            return { oscillator, gainNode, filterNode };
        } catch (error) {
            console.warn('Error creating music note:', error);
            return null;
        }
    }

    // Create drum sound
    function createDrumSound(startTime, type = 'kick') {
        if (!musicEnabled || !audioContext || !musicGainNode) return null;

        try {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            const filterNode = audioContext.createBiquadFilter();

            if (type === 'kick') {
                oscillator.frequency.setValueAtTime(60, startTime);
                oscillator.frequency.exponentialRampToValueAtTime(30, startTime + 0.1);
                filterNode.frequency.setValueAtTime(100, startTime);
            } else if (type === 'snare') {
                oscillator.type = 'noise';
                filterNode.frequency.setValueAtTime(2000, startTime);
                filterNode.Q.setValueAtTime(1, startTime);
            }

            oscillator.type = type === 'kick' ? 'sine' : 'sawtooth';
            filterNode.type = 'lowpass';

            oscillator.connect(filterNode);
            filterNode.connect(gainNode);
            gainNode.connect(musicGainNode);

            gainNode.gain.setValueAtTime(0, startTime);
            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + 0.2);

            oscillator.start(startTime);
            oscillator.stop(startTime + 0.2);

            return { oscillator, gainNode, filterNode };
        } catch (error) {
            console.warn('Error creating drum sound:', error);
            return null;
        }
    }

    // Generate a musical phrase
    function generateMusicPhrase(startTime, pattern) {
        const beatDuration = 0.25; // 16th notes
        const phraseDuration = beatDuration * 16; // 4 beats

        // Bass line
        for (let i = 0; i < 8; i++) {
            if (i % 2 === 0) {
                const note = MUSIC_SCALES.bass[pattern % MUSIC_SCALES.bass.length];
                createMusicNote(note, startTime + i * beatDuration * 2, beatDuration * 1.5, 'square', 0.15);
            }
        }

        // Lead melody
        for (let i = 0; i < 16; i++) {
            if (Math.random() < 0.4) {
                const noteIndex = (pattern + i) % MUSIC_SCALES.lead.length;
                const note = MUSIC_SCALES.lead[noteIndex];
                createMusicNote(note, startTime + i * beatDuration, beatDuration * 0.8, 'square', 0.08);
            }
        }

        // Drums
        const drumPattern = DRUM_PATTERNS[pattern % DRUM_PATTERNS.length];
        for (let i = 0; i < drumPattern.length; i++) {
            if (drumPattern[i]) {
                createDrumSound(startTime + i * beatDuration * 2, i % 4 === 0 ? 'kick' : 'snare');
            }
        }

        return phraseDuration;
    }

    // Start background music
    function startBackgroundMusic() {
        if (!musicEnabled || !audioContext) return;

        const scheduleMusic = () => {
            const currentTime = audioContext.currentTime;
            const phraseDuration = generateMusicPhrase(currentTime, musicPattern);

            musicPattern = (musicPattern + 1) % 8; // Cycle through patterns

            // Schedule next phrase
            setTimeout(scheduleMusic, phraseDuration * 1000);
        };

        scheduleMusic();
    }

    // Music volume control
    function adjustMusicVolume(delta) {
        if (!musicGainNode) return;

        musicVolume = Math.max(0, Math.min(1, musicVolume + delta));
        musicGainNode.gain.setValueAtTime(musicVolume, audioContext.currentTime);

        showGameStatus(`Music Volume: ${Math.round(musicVolume * 100)}%`, 1000);
    }

    // Toggle music on/off
    function toggleMusic() {
        musicEnabled = !musicEnabled;

        if (musicGainNode) {
            musicGainNode.gain.setValueAtTime(musicEnabled ? musicVolume : 0, audioContext.currentTime);
        }

        // Start music if enabling and not already started
        if (musicEnabled && audioContext && audioContext.state === 'running' && !musicStarted) {
            startBackgroundMusic();
            musicStarted = true;
        }

        showGameStatus(`Music: ${musicEnabled ? 'ON' : 'OFF'}`, 1000);
    }

    // Enhanced cyberpunk sound effects
    function playCyberpunkSound(type, intensity = 1) {
        if (!soundEnabled || !audioContext) return;

        try {
            const currentTime = audioContext.currentTime;
            let config = {};

            switch (type) {
                case 'paddle_left':
                    config = {
                        frequency: 400 + (intensity * 100),
                        duration: 0.15,
                        type: 'sawtooth',
                        volume: 0.12,
                        filterFreq: 800,
                        modulation: true
                    };
                    break;

                case 'paddle_right':
                    config = {
                        frequency: 600 + (intensity * 150),
                        duration: 0.15,
                        type: 'square',
                        volume: 0.12,
                        filterFreq: 1200,
                        modulation: true
                    };
                    break;

                case 'wall_top':
                    config = {
                        frequency: 800,
                        duration: 0.1,
                        type: 'triangle',
                        volume: 0.08,
                        filterFreq: 1600,
                        sweep: true
                    };
                    break;

                case 'wall_bottom':
                    config = {
                        frequency: 600,
                        duration: 0.1,
                        type: 'triangle',
                        volume: 0.08,
                        filterFreq: 1200,
                        sweep: true
                    };
                    break;

                case 'score_player':
                    config = {
                        frequency: 300,
                        duration: 0.8,
                        type: 'sine',
                        volume: 0.15,
                        filterFreq: 600,
                        chord: [300, 400, 500]
                    };
                    break;

                case 'score_ai':
                    config = {
                        frequency: 200,
                        duration: 0.8,
                        type: 'triangle',
                        volume: 0.15,
                        filterFreq: 400,
                        chord: [200, 250, 300]
                    };
                    break;

                case 'win_player':
                    config = {
                        frequency: 400,
                        duration: 1.2,
                        type: 'sine',
                        volume: 0.2,
                        filterFreq: 800,
                        chord: [400, 500, 600, 800]
                    };
                    break;

                case 'win_ai':
                    config = {
                        frequency: 150,
                        duration: 1.2,
                        type: 'sawtooth',
                        volume: 0.2,
                        filterFreq: 300,
                        chord: [150, 200, 250]
                    };
                    break;

                case 'enhancement_milestone':
                    config = {
                        frequency: 800 + (intensity * 200),
                        duration: 0.5,
                        type: 'square',
                        volume: 0.15,
                        filterFreq: 1600,
                        chord: [800, 1000, 1200, 1600].map(f => f + (intensity * 100))
                    };
                    break;

                case 'paddle_levelup':
                    config = {
                        frequency: 600 + (intensity * 150),
                        duration: 0.6,
                        type: 'sawtooth',
                        volume: 0.18,
                        filterFreq: 1200,
                        chord: [600, 800, 1000].map(f => f + (intensity * 100)),
                        modulation: true
                    };
                    break;

                case 'upgrade_applied':
                    config = {
                        frequency: 800 + (intensity * 200),
                        duration: 0.8,
                        type: 'square',
                        volume: 0.2,
                        filterFreq: 1600,
                        chord: [800, 1000, 1200, 1600],
                        modulation: true,
                        sweep: true
                    };
                    break;

                default:
                    return;
            }

            // Create main sound or chord
            if (config.chord) {
                config.chord.forEach((freq, index) => {
                    createSoundOscillator(freq, currentTime + index * 0.1, config);
                });
            } else {
                createSoundOscillator(config.frequency, currentTime, config);
            }

        } catch (error) {
            console.warn('Error playing cyberpunk sound:', error);
            soundEnabled = false;
        }
    }

    // Create individual sound oscillator with effects
    function createSoundOscillator(frequency, startTime, config) {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        const filterNode = audioContext.createBiquadFilter();

        // Set up oscillator
        oscillator.type = config.type;
        oscillator.frequency.setValueAtTime(frequency, startTime);

        // Add frequency modulation for cyberpunk feel
        if (config.modulation) {
            const lfo = audioContext.createOscillator();
            const lfoGain = audioContext.createGain();

            lfo.frequency.setValueAtTime(8, startTime);
            lfo.type = 'sine';
            lfoGain.gain.setValueAtTime(frequency * 0.1, startTime);

            lfo.connect(lfoGain);
            lfoGain.connect(oscillator.frequency);

            lfo.start(startTime);
            lfo.stop(startTime + config.duration);
        }

        // Add frequency sweep
        if (config.sweep) {
            oscillator.frequency.exponentialRampToValueAtTime(frequency * 0.5, startTime + config.duration);
        }

        // Set up filter
        filterNode.type = 'lowpass';
        filterNode.frequency.setValueAtTime(config.filterFreq, startTime);
        filterNode.Q.setValueAtTime(2, startTime);

        // Connect nodes
        oscillator.connect(filterNode);
        filterNode.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // Envelope with configurable volume
        const adjustedVolume = config.volume * CONFIG.SOUND_VOLUME;
        gainNode.gain.setValueAtTime(0, startTime);
        gainNode.gain.linearRampToValueAtTime(adjustedVolume, startTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(adjustedVolume * 0.3, startTime + config.duration * 0.7);
        gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + config.duration);

        oscillator.start(startTime);
        oscillator.stop(startTime + config.duration);
    }

    // Legacy sound function for compatibility
    function playSound(frequency, duration, type = 'sine') {
        if (!soundEnabled || !audioContext) return;

        try {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.type = type;

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        } catch (error) {
            console.warn('Error playing sound:', error);
            soundEnabled = false; // Disable sound on error
        }
    }

    // Main game loop with error handling
    function gameLoop() {
        try {
            if (!gameRunning) return;

            // Handle keyboard input
            handleKeyboardInput();

            // Handle glitch effects (reduced frequency for better gameplay)
            if (Date.now() - lastGlitchTime > GLITCH_INTERVAL) {
                applyGlitch();
                lastGlitchTime = Date.now();
            }

            // Apply upgrade effects
            applyMagneticEffects();
            applyDecelZoneEffects();
            applyAutoAssist();
            updateHelperPaddles();
            checkSlowMotion();
            updateScoreMultipliers();

            // Apply curve effect to main ball
            if (ball.curve) {
                ball.vy += ball.curve;
                ball.curve *= 0.98; // Decay curve over time
            }

            // Update ball position with slow motion effect
            const timeMultiplier = slowMotionActive ? 0.5 : 1;
            ball.x += ball.vx * timeMultiplier;
            ball.y += ball.vy * timeMultiplier;

            // Update multi-balls
            updateMultiBalls();

            // Create trail particles
            createTrailParticles();

        // Wall collisions with better bounce physics (no streak reset)
        if (ball.y <= 0) {
            ball.y = 0;
            ball.vy = Math.abs(ball.vy);
            createGlitchEffect();
            playCyberpunkSound('wall_top');
            // Wall hits do not reset progressive system - streaks continue
        } else if (ball.y >= canvas.height - BALL_S) {
            ball.y = canvas.height - BALL_S;
            ball.vy = -Math.abs(ball.vy);
            createGlitchEffect();
            playCyberpunkSound('wall_bottom');
            // Wall hits do not reset progressive system - streaks continue
        }

        // Improved paddle collision detection with upgrade support
        const leftEffects = activeUpgradeEffects.left;
        const rightEffects = activeUpgradeEffects.right;

        const leftLengthMult = leftEffects.lengthMultiplier || 1;
        const leftWidthMult = leftEffects.widthMultiplier || 1;
        const rightLengthMult = rightEffects.lengthMultiplier || 1;
        const rightWidthMult = rightEffects.widthMultiplier || 1;

        const leftPaddleW = PADDLE_W * leftWidthMult;
        const leftPaddleH = PADDLE_H * leftLengthMult;
        const rightPaddleW = PADDLE_W * rightWidthMult;
        const rightPaddleH = PADDLE_H * rightLengthMult;

        const leftHit = ball.vx < 0 &&
            ball.x <= paddles.left.x + leftPaddleW &&
            ball.x + BALL_S >= paddles.left.x &&
            ball.y + BALL_S >= paddles.left.y &&
            ball.y <= paddles.left.y + leftPaddleH;

        const rightHit = ball.vx > 0 &&
            ball.x + BALL_S >= paddles.right.x &&
            ball.x <= paddles.right.x + rightPaddleW &&
            ball.y + BALL_S >= paddles.right.y &&
            ball.y <= paddles.right.y + rightPaddleH;

        if (leftHit || rightHit) {
            // Calculate hit position for angle variation with upgraded paddle size
            const paddle = leftHit ? paddles.left : paddles.right;
            const paddleHeight = leftHit ? leftPaddleH : rightPaddleH;
            const hitPos = (ball.y + BALL_S/2 - paddle.y - paddleHeight/2) / (paddleHeight/2);

            // Progressive speed system - configurable increase per hit
            consecutiveHits++;
            ballSpeedMultiplier *= CONFIG.BALL_SPEED_INCREMENT;

            // Apply offensive upgrades
            const hitPaddleSide = leftHit ? 'left' : 'right';
            const hitEffects = activeUpgradeEffects[hitPaddleSide];

            // Ball accelerator upgrade
            let speedBoost = hitEffects.speedBoost || 1;

            // Reverse and modify ball velocity
            ball.vx = -ball.vx * CONFIG.BALL_SPEED_INCREMENT * speedBoost;
            ball.vy = hitPos * 4; // Angle based on hit position

            // Apply speed multiplier for cumulative effect
            ball.vx *= ballSpeedMultiplier;
            ball.vy *= ballSpeedMultiplier;

            // Curve ball upgrade
            if (hitEffects.curveStrength) {
                ball.curve = hitEffects.curveStrength * (Math.random() > 0.5 ? 1 : -1);
            }

            // Multi-ball upgrade
            if (hitEffects.splitChance && Math.random() < hitEffects.splitChance) {
                createMultiBall(hitEffects);
            }

            // Clamp maximum speed to prevent game breaking
            const maxSpeed = MAX_SPEED * 2; // Allow higher speeds
            if (Math.abs(ball.vx) > maxSpeed) {
                ball.vx = ball.vx > 0 ? maxSpeed : -maxSpeed;
            }
            if (Math.abs(ball.vy) > maxSpeed) {
                ball.vy = ball.vy > 0 ? maxSpeed : -maxSpeed;
            }

            // Visual enhancement triggers every 3 hits
            if (consecutiveHits % 3 === 0) {
                visualEnhancementLevel++;
                ballTrailIntensity = Math.min(visualEnhancementLevel * 0.3, 1.0);
                createEnhancementEffect();
                playCyberpunkSound('enhancement_milestone', visualEnhancementLevel);
            }

            // Streak logic and paddle level-up system
            if (leftHit) {
                leftStreak++;
                rightStreak = 0;
                updatePaddleLevels('left');
            } else {
                rightStreak++;
                leftStreak = 0;
                updatePaddleLevels('right');
            }

            // Create particles on hit
            createParticles();

            // Play enhanced paddle hit sound
            const soundType = leftHit ? 'paddle_left' : 'paddle_right';
            const intensity = Math.min(Math.abs(ball.vx) / MAX_SPEED, 1);
            playCyberpunkSound(soundType, intensity);
        }

        // Score check with win condition
        if (ball.x < -BALL_S) {
            // Apply score multiplier for right paddle
            const rightMultiplier = scoreMultipliers.right;
            const points = rightMultiplier.active ? rightMultiplier.multiplier : 1;
            score.right += points;

            if (rightMultiplier.active) {
                rightMultiplier.hitsLeft--;
                showGameStatus(`AI SCORES ${points}x POINTS!`, 1500);
            }

            resetBall();
            leftStreak = 0;
            rightStreak = 0;
            resetProgressiveSystem(); // Reset on score
            playSound(200, 0.5, 'triangle'); // Score sound

            if (score.right >= WINNING_SCORE) {
                gameWon = true;
                gameRunning = false;
                showGameStatus(`🤖 AI WINS! Final Score: ${score.left} - ${score.right}\nPress R to Restart`, 0);
                playCyberpunkSound('win_ai');
                screenShake = 10; // Strong shake for win
            } else {
                showGameStatus(`AI Scores! ${score.left} - ${score.right}`, 1500);
                playCyberpunkSound('score_ai');
                screenShake = 5; // Mild shake for score
            }
        } else if (ball.x > canvas.width) {
            // Apply score multiplier for left paddle
            const leftMultiplier = scoreMultipliers.left;
            const points = leftMultiplier.active ? leftMultiplier.multiplier : 1;
            score.left += points;

            if (leftMultiplier.active) {
                leftMultiplier.hitsLeft--;
                showGameStatus(`PLAYER SCORES ${points}x POINTS!`, 1500);
            }

            resetBall();
            leftStreak = 0;
            rightStreak = 0;
            resetProgressiveSystem(); // Reset on score
            playSound(300, 0.5, 'triangle'); // Score sound

            if (score.left >= WINNING_SCORE) {
                gameWon = true;
                gameRunning = false;
                showGameStatus(`🎉 PLAYER WINS! Final Score: ${score.left} - ${score.right}\nPress R to Restart`, 0);
                playCyberpunkSound('win_player');
                screenShake = 10; // Strong shake for win
            } else {
                showGameStatus(`Player Scores! ${score.left} - ${score.right}`, 1500);
                playCyberpunkSound('score_player');
                screenShake = 5; // Mild shake for score
            }
        }

        // Enhanced AI with predictive movement and difficulty scaling
        let aiSpeed = CONFIG.AI_SENSITIVITY;
        let targetY = ball.y - PADDLE_H / 2;

        // Predict ball position for better AI
        if (ball.vx > 0) { // Ball moving towards AI
            const timeToReach = (paddles.right.x - ball.x) / ball.vx;
            const predictedY = ball.y + (ball.vy * timeToReach);
            targetY = predictedY - PADDLE_H / 2;
        }

        // Add difficulty scaling based on score difference
        const scoreDiff = score.right - score.left;
        if (scoreDiff > 2) {
            aiSpeed *= 0.7; // Make AI easier if it's winning
        } else if (scoreDiff < -2) {
            aiSpeed *= 1.3; // Make AI harder if it's losing
        }

        // Add slight randomness and reaction delay
        const randomOffset = (Math.random() - 0.5) * 20;
        targetY += randomOffset;
        aiSpeed += (Math.random() * 0.02 - 0.01);

        // Smooth AI movement
        paddles.right.y += (targetY - paddles.right.y) * aiSpeed;

        // Keep AI paddle in bounds
        if (paddles.right.y < 0) paddles.right.y = 0;
        if (paddles.right.y + PADDLE_H > canvas.height) paddles.right.y = canvas.height - PADDLE_H;

            // Performance monitoring
            frameCount++;
            const now = Date.now();
            if (now - lastFpsTime >= 1000) {
                currentFps = frameCount;
                frameCount = 0;
                lastFpsTime = now;

                // Performance optimization - reduce particles if FPS drops
                if (currentFps < 45) {
                    particles.splice(0, Math.floor(particles.length * 0.3));
                    enhancementParticles.splice(0, Math.floor(enhancementParticles.length * 0.5));
                }
            }

            // UI elements updated automatically through rendering

            // Render
            render();

            // Continue the game loop
            animationId = requestAnimationFrame(gameLoop);
        } catch (error) {
            console.error('Error in game loop:', error);
            // Try to continue the game loop despite errors
            animationId = requestAnimationFrame(gameLoop);
        }
    }

    // Reset ball with better positioning
    function resetBall() {
        ball.x = canvas.width / 2 - BALL_S / 2;
        ball.y = canvas.height / 2 - BALL_S / 2;

        // Random direction but ensure minimum speed
        const angle = (Math.random() * Math.PI/3) - Math.PI/6; // ±30 degrees
        const speed = 3 + Math.random() * 2; // 3-5 speed
        const direction = Math.random() < 0.5 ? 1 : -1;

        ball.vx = Math.cos(angle) * speed * direction;
        ball.vy = Math.sin(angle) * speed;
    }

    // Reset progressive speed and visual enhancement system
    function resetProgressiveSystem() {
        consecutiveHits = 0;
        ballSpeedMultiplier = 1.0;
        visualEnhancementLevel = 0;
        ballTrailIntensity = 0;
        enhancementParticles = [];
    }

    // Create enhancement effect for milestone achievements
    function createEnhancementEffect() {
        // Performance check - limit enhancement particles
        if (enhancementParticles.length > CONFIG.MAX_ENHANCEMENT_PARTICLES) {
            enhancementParticles.splice(0, enhancementParticles.length - CONFIG.MAX_ENHANCEMENT_PARTICLES);
        }

        const centerX = ball.x + BALL_S / 2;
        const centerY = ball.y + BALL_S / 2;

        // Create energy rings (reduced count for performance)
        for (let ring = 0; ring < 2; ring++) {
            for (let i = 0; i < 8; i++) {
                const angle = (Math.PI * 2 * i) / 8;
                const radius = 20 + ring * 15;
                const speed = 2 + ring;

                enhancementParticles.push({
                    x: centerX + Math.cos(angle) * radius,
                    y: centerY + Math.sin(angle) * radius,
                    vx: Math.cos(angle) * speed,
                    vy: Math.sin(angle) * speed,
                    radius: 3 - ring,
                    life: 1.0,
                    decay: 0.025, // Faster decay for performance
                    color: `rgba(${255 - ring * 50}, ${100 + ring * 50}, 255, 0.8)`,
                    type: 'enhancement'
                });
            }
        }

        // Screen distortion effect
        screenShake = 3;

        // Show streak counter
        showGameStatus(`🔥 ${consecutiveHits} Hit Streak! Speed Boost!`, 1000);
    }

    // Optimized particle creation with performance limits
    function createParticles() {
        // Limit total particles for performance
        if (particles.length > CONFIG.MAX_PARTICLES) {
            particles.splice(0, particles.length - CONFIG.MAX_PARTICLES);
        }

        const count = Math.floor(Math.random() * 15) + 10;
        const colors = [
            'rgba(0, 255, 255, 0.8)',
            'rgba(255, 0, 128, 0.8)',
            'rgba(0, 255, 0, 0.8)',
            'rgba(255, 255, 0, 0.8)',
            'rgba(255, 255, 255, 0.9)'
        ];

        try {
            for (let i = 0; i < count && particles.length < CONFIG.MAX_PARTICLES; i++) {
                const angle = (Math.PI * 2 * i) / count + Math.random() * 0.5;
                const speed = Math.random() * 4 + 2;

                particles.push({
                    x: ball.x + BALL_S / 2,
                    y: ball.y + BALL_S / 2,
                    radius: Math.random() * 4 + 2,
                    speedX: Math.cos(angle) * speed,
                    speedY: Math.sin(angle) * speed,
                    color: colors[Math.floor(Math.random() * colors.length)],
                    life: 1.0,
                    decay: Math.random() * 0.02 + 0.01
                });
            }
        } catch (error) {
            console.warn('Error creating particles:', error);
        }
    }

    // Enhanced trail particles for ball with progressive intensity
    function createTrailParticles() {
        // Skip if visual enhancements are disabled
        if (!CONFIG.VISUAL_ENHANCEMENTS) return;

        const baseChance = 0.2 + ballTrailIntensity * 0.3; // Increase with enhancement level
        const particleCount = 1 + Math.floor(ballTrailIntensity * 3); // More particles at higher levels

        for (let p = 0; p < particleCount; p++) {
            if (Math.random() < baseChance && particles.length < CONFIG.MAX_PARTICLES) {
                try {
                    const intensity = ballTrailIntensity;
                    const colors = [
                        `rgba(255, 0, 128, ${0.6 + intensity * 0.4})`,
                        `rgba(0, 255, 255, ${0.6 + intensity * 0.4})`,
                        `rgba(255, 255, 0, ${0.6 + intensity * 0.4})`,
                        `rgba(255, 255, 255, ${0.8 + intensity * 0.2})`
                    ];

                    particles.push({
                        x: ball.x + BALL_S / 2 + (Math.random() - 0.5) * BALL_S,
                        y: ball.y + BALL_S / 2 + (Math.random() - 0.5) * BALL_S,
                        radius: (Math.random() * 2 + 1) * (1 + intensity),
                        speedX: (Math.random() - 0.5) * (1 + intensity),
                        speedY: (Math.random() - 0.5) * (1 + intensity),
                        color: colors[Math.floor(Math.random() * colors.length)],
                        life: 0.5 + intensity * 0.3,
                        decay: 0.02 - intensity * 0.005,
                        type: 'trail'
                    });
                } catch (error) {
                    console.warn('Error creating trail particles:', error);
                }
            }
        }
    }

    // Glitch effect state
    let glitchActive = false;
    let glitchIntensity = 0;
    let screenShake = 0;

    // Apply glitch effect (non-disruptive)
    function applyGlitch() {
        glitchActive = true;
        glitchIntensity = 0.3;

        // Fade out glitch over time
        setTimeout(() => {
            glitchActive = false;
            glitchIntensity = 0;
        }, 200);
    }

    // Create glitch effect on collision (subtle)
    function createGlitchEffect() {
        glitchActive = true;
        glitchIntensity = 0.1;

        // Quick fade
        setTimeout(() => {
            glitchActive = false;
            glitchIntensity = 0;
        }, 100);
    }

    // Render game
    function render() {
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Apply screen shake effect
        if (screenShake > 0) {
            ctx.save();
            const shakeX = (Math.random() - 0.5) * screenShake;
            const shakeY = (Math.random() - 0.5) * screenShake;
            ctx.translate(shakeX, shakeY);
            screenShake *= 0.9; // Decay shake
            if (screenShake < 0.1) screenShake = 0;
        }

        // Apply glitch effect if active
        if (glitchActive) {
            ctx.save();
            ctx.globalAlpha = glitchIntensity;

            // Random color overlay
            ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.1)`;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Random pixel noise
            for (let i = 0; i < 50; i++) {
                ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255}, 0.3)`;
                ctx.fillRect(Math.random() * canvas.width, Math.random() * canvas.height, 2, 2);
            }

            ctx.restore();
        }

        // Draw background grid
        ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
        ctx.lineWidth = 0.5;
        for (let i = 0; i < canvas.width; i += 50) {
            ctx.beginPath();
            ctx.moveTo(i, 0);
            ctx.lineTo(i, canvas.height);
            ctx.stroke();
        }
        for (let i = 0; i < canvas.height; i += 50) {
            ctx.beginPath();
            ctx.moveTo(0, i);
            ctx.lineTo(canvas.width, i);
            ctx.stroke();
        }

        // Draw center line with neon flicker
        ctx.strokeStyle = `rgba(0, 255, 255, ${0.3 + Math.sin(Date.now() * 0.01) * 0.1})`;
        ctx.lineWidth = 2;
        ctx.setLineDash([10, 15]);
        ctx.beginPath();
        ctx.moveTo(canvas.width / 2, 0);
        ctx.lineTo(canvas.width / 2, canvas.height);
        ctx.stroke();
        ctx.setLineDash([]);

        // Update and draw regular particles with enhanced effects
        for (let i = particles.length - 1; i >= 0; i--) {
            const p = particles[i];

            // Update particle properties
            p.x += p.speedX;
            p.y += p.speedY;
            p.speedX *= 0.99; // Slight friction
            p.speedY *= 0.99;

            if (p.life !== undefined) {
                p.life -= p.decay;
                p.radius *= 0.995;
            } else {
                p.radius *= 0.98; // Legacy particles
            }

            // Draw particle with enhanced glow
            if (p.life === undefined || p.life > 0) {
                ctx.save();

                const alpha = p.life !== undefined ? p.life : (p.radius / 4);
                ctx.globalAlpha = alpha;

                // Enhanced glow for trail particles
                const glowIntensity = p.type === 'trail' ? 20 + ballTrailIntensity * 10 : 15;
                ctx.shadowBlur = glowIntensity;
                ctx.shadowColor = p.color;
                ctx.fillStyle = p.color;
                ctx.beginPath();
                ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                ctx.fill();

                // Inner bright core
                ctx.shadowBlur = 5;
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.beginPath();
                ctx.arc(p.x, p.y, p.radius * 0.3, 0, Math.PI * 2);
                ctx.fill();

                ctx.restore();
            }

            // Remove dead particles
            const shouldRemove = p.life !== undefined ?
                p.life <= 0 :
                (p.radius < 0.5 || p.x < -20 || p.x > canvas.width + 20 || p.y < -20 || p.y > canvas.height + 20);

            if (shouldRemove) {
                particles.splice(i, 1);
            }
        }

        // Update and draw enhancement particles
        for (let i = enhancementParticles.length - 1; i >= 0; i--) {
            const p = enhancementParticles[i];

            // Update enhancement particle
            p.x += p.vx;
            p.y += p.vy;
            p.life -= p.decay;
            p.radius *= 0.99;

            // Draw enhancement particle with special effects
            if (p.life > 0) {
                ctx.save();
                ctx.globalAlpha = p.life;

                // Chromatic aberration effect
                ctx.shadowBlur = 25;
                ctx.shadowColor = p.color;

                // Draw multiple offset circles for chromatic effect
                const offset = 2;
                ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
                ctx.beginPath();
                ctx.arc(p.x - offset, p.y, p.radius, 0, Math.PI * 2);
                ctx.fill();

                ctx.fillStyle = 'rgba(0, 255, 0, 0.3)';
                ctx.beginPath();
                ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                ctx.fill();

                ctx.fillStyle = 'rgba(0, 0, 255, 0.3)';
                ctx.beginPath();
                ctx.arc(p.x + offset, p.y, p.radius, 0, Math.PI * 2);
                ctx.fill();

                // Main particle
                ctx.fillStyle = p.color;
                ctx.beginPath();
                ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                ctx.fill();

                ctx.restore();
            }

            // Remove dead enhancement particles
            if (p.life <= 0 || p.radius < 0.5) {
                enhancementParticles.splice(i, 1);
            }
        }

        // Draw prediction trails first (behind other elements)
        drawPredictionTrail();

        // Draw helper paddles (behind main paddles)
        drawHelperPaddles();

        // Draw paddles with dynamic glow and level indicators
        drawPaddle(paddles.left.x, paddles.left.y, leftStreak);
        drawPaddle(paddles.right.x, paddles.right.y, rightStreak);

        // Draw paddle level indicators
        drawPaddleLevelIndicator('left', paddleLevels.left);
        drawPaddleLevelIndicator('right', paddleLevels.right);

        // Draw active upgrade indicators
        drawUpgradeIndicators();

        // Draw ball with enhanced neon effect
        ctx.save();
        ctx.shadowBlur = 15;
        ctx.shadowColor = '#ff0080';
        ctx.fillStyle = '#ff0080';

        // Draw ball (fallback for browsers without roundRect)
        if (ctx.roundRect) {
            ctx.beginPath();
            ctx.roundRect(ball.x, ball.y, BALL_S, BALL_S, 2);
            ctx.fill();
        } else {
            ctx.fillRect(ball.x, ball.y, BALL_S, BALL_S);
        }

        // Add inner glow
        ctx.shadowBlur = 5;
        ctx.shadowColor = '#ffffff';
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fill();
        ctx.restore();

        // Draw multi-balls
        drawMultiBalls();

        // Draw touch indicator for mobile feedback
        drawTouchIndicator();

        // Update score
        scoreEl.textContent = `${score.left} : ${score.right}`;

        // Restore context if screen shake was applied
        if (screenShake > 0) {
            ctx.restore();
        }
    }

    // Enhanced paddle drawing with permanent level-up effects and upgrades
    function drawPaddle(x, y, streak) {
        const paddleSide = (x < canvas.width / 2) ? 'left' : 'right';
        const level = paddleLevels[paddleSide];
        const effects = activeUpgradeEffects[paddleSide];

        // Base colors that change with level
        const baseColors = [
            'rgba(0, 255, 255, 0.9)',      // Level 0 - Cyan
            'rgba(0, 255, 128, 0.9)',      // Level 1 - Green-Cyan
            'rgba(128, 255, 0, 0.9)',      // Level 2 - Yellow-Green
            'rgba(255, 255, 0, 0.9)',      // Level 3 - Yellow
            'rgba(255, 128, 0, 0.9)',      // Level 4 - Orange
            'rgba(255, 0, 128, 0.9)',      // Level 5+ - Magenta
        ];

        const colorIndex = Math.min(level, baseColors.length - 1);
        const baseColor = baseColors[colorIndex];
        const glowIntensity = 10 + level * 5; // Increased glow with level

        ctx.save();

        // Enhanced glow effect based on level
        ctx.shadowBlur = glowIntensity;
        ctx.shadowColor = baseColor;

        // Calculate paddle dimensions with upgrades
        const lengthMultiplier = effects.lengthMultiplier || 1;
        const widthMultiplier = effects.widthMultiplier || 1;

        const paddleWidth = PADDLE_W * widthMultiplier + level * 2;
        const paddleHeight = PADDLE_H * lengthMultiplier + level * 4;
        const adjustedX = x - (level + (widthMultiplier - 1) * PADDLE_W / 2);
        const adjustedY = y - (level * 2 + (lengthMultiplier - 1) * PADDLE_H / 2);

        ctx.fillStyle = baseColor;
        ctx.fillRect(adjustedX, adjustedY, paddleWidth, paddleHeight);

        // Add inner highlight
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fillRect(adjustedX + 1, adjustedY + 1, paddleWidth - 2, paddleHeight - 2);

        // Level-based visual enhancements
        if (level > 0) {
            // Energy aura rings
            for (let ring = 0; ring < Math.min(level, 3); ring++) {
                const ringRadius = 15 + ring * 8;
                const ringAlpha = 0.3 - ring * 0.1;

                ctx.strokeStyle = `rgba(255, 255, 255, ${ringAlpha})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(adjustedX + paddleWidth / 2, adjustedY + paddleHeight / 2, ringRadius, 0, Math.PI * 2);
                ctx.stroke();
            }

            // Particle corona effect for high levels
            if (level >= 3) {
                const time = Date.now() * 0.005;
                for (let i = 0; i < level * 2; i++) {
                    const angle = (Math.PI * 2 * i) / (level * 2) + time;
                    const radius = 25 + Math.sin(time + i) * 5;
                    const particleX = adjustedX + paddleWidth / 2 + Math.cos(angle) * radius;
                    const particleY = adjustedY + paddleHeight / 2 + Math.sin(angle) * radius;

                    ctx.fillStyle = `rgba(255, 255, 255, ${0.6 + Math.sin(time * 2 + i) * 0.3})`;
                    ctx.beginPath();
                    ctx.arc(particleX, particleY, 2, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            // Energy trails for very high levels
            if (level >= 5) {
                const pulse = Math.sin(Date.now() * 0.01) * 0.5 + 0.5;
                ctx.fillStyle = `rgba(255, 255, 255, ${pulse * 0.4})`;
                ctx.fillRect(adjustedX, adjustedY, paddleWidth, paddleHeight);
            }
        }

        ctx.restore();
    }

    // Draw paddle level indicator
    function drawPaddleLevelIndicator(paddleSide, level) {
        if (level === 0) return; // No indicator for level 0

        const paddle = paddles[paddleSide];
        const isLeft = paddleSide === 'left';
        const indicatorX = isLeft ? paddle.x - 25 : paddle.x + PADDLE_W + 15;
        const indicatorY = paddle.y - 10;

        ctx.save();

        // Draw level number
        ctx.fillStyle = `rgba(255, 255, 255, 0.9)`;
        ctx.font = 'bold 14px monospace';
        ctx.textAlign = isLeft ? 'right' : 'left';
        ctx.fillText(`LV${level}`, indicatorX, indicatorY);

        // Draw level stars/dots
        for (let i = 0; i < Math.min(level, 5); i++) {
            const starX = indicatorX + (isLeft ? -15 - i * 8 : 5 + i * 8);
            const starY = indicatorY + 15;

            ctx.fillStyle = `rgba(255, 215, 0, 0.8)`; // Gold color
            ctx.beginPath();
            ctx.arc(starX, starY, 3, 0, Math.PI * 2);
            ctx.fill();

            // Add glow effect
            ctx.shadowBlur = 8;
            ctx.shadowColor = 'rgba(255, 215, 0, 0.6)';
            ctx.beginPath();
            ctx.arc(starX, starY, 2, 0, Math.PI * 2);
            ctx.fill();
        }

        // Show "MAX" for very high levels
        if (level > 5) {
            ctx.fillStyle = `rgba(255, 0, 255, 0.9)`;
            ctx.font = 'bold 10px monospace';
            ctx.fillText('MAX', indicatorX, indicatorY + 30);
        }

        ctx.restore();
    }

    // Draw active upgrade indicators
    function drawUpgradeIndicators() {
        const indicatorSize = 30;
        const spacing = 35;
        const startY = 50;

        ['left', 'right'].forEach(paddleSide => {
            const upgrades = paddleUpgrades[paddleSide];
            const isLeft = paddleSide === 'left';
            const startX = isLeft ? 20 : canvas.width - 20 - indicatorSize;

            upgrades.forEach((upgrade, index) => {
                const definition = UPGRADE_DEFINITIONS[upgrade.id];
                if (!definition) return;

                const x = startX;
                const y = startY + (index * spacing);

                ctx.save();

                // Background circle
                ctx.fillStyle = `rgba(${definition.rarity.color.slice(1).match(/.{2}/g).map(hex => parseInt(hex, 16)).join(', ')}, 0.3)`;
                ctx.beginPath();
                ctx.arc(x + indicatorSize/2, y + indicatorSize/2, indicatorSize/2, 0, Math.PI * 2);
                ctx.fill();

                // Border
                ctx.strokeStyle = definition.rarity.color;
                ctx.lineWidth = 2;
                ctx.stroke();

                // Icon
                ctx.fillStyle = '#ffffff';
                ctx.font = '16px monospace';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(definition.icon, x + indicatorSize/2, y + indicatorSize/2);

                // Stack indicator
                if (upgrade.stacks > 1) {
                    ctx.fillStyle = '#ffff00';
                    ctx.font = '10px monospace';
                    ctx.textAlign = 'center';
                    ctx.fillText(`x${upgrade.stacks}`, x + indicatorSize/2, y + indicatorSize + 8);
                }

                // Glow effect
                ctx.shadowBlur = 5;
                ctx.shadowColor = definition.rarity.color;
                ctx.stroke();

                ctx.restore();
            });

            // Draw paddle side label
            if (upgrades.length > 0) {
                ctx.save();
                ctx.fillStyle = '#00ffff';
                ctx.font = '12px monospace';
                ctx.textAlign = isLeft ? 'left' : 'right';
                ctx.fillText(isLeft ? 'PLAYER' : 'AI', isLeft ? 20 : canvas.width - 20, 35);
                ctx.restore();
            }
        });

        // Draw slow motion indicator
        if (slowMotionActive) {
            ctx.save();
            ctx.fillStyle = 'rgba(255, 255, 0, 0.8)';
            ctx.font = '20px monospace';
            ctx.textAlign = 'center';
            ctx.fillText('⏰ TIME DILATION ACTIVE', canvas.width / 2, 30);
            ctx.restore();
        }

        // Draw score multiplier indicators
        ['left', 'right'].forEach(paddleSide => {
            const multiplier = scoreMultipliers[paddleSide];
            if (multiplier.active) {
                const isLeft = paddleSide === 'left';
                const x = isLeft ? 100 : canvas.width - 100;
                const y = canvas.height - 50;

                ctx.save();
                ctx.fillStyle = 'rgba(255, 215, 0, 0.9)';
                ctx.font = '16px monospace';
                ctx.textAlign = 'center';
                ctx.fillText(`${multiplier.multiplier}x POINTS (${multiplier.hitsLeft} left)`, x, y);
                ctx.restore();
            }
        });
    }

    // Draw touch indicator for mobile feedback
    function drawTouchIndicator() {
        if (touchIndicator.active && Date.now() < touchIndicator.fadeTime) {
            const timeLeft = touchIndicator.fadeTime - Date.now();
            const alpha = Math.min(1, timeLeft / 500); // Fade over 500ms

            ctx.save();
            ctx.globalAlpha = alpha;

            // Draw touch ripple effect
            ctx.strokeStyle = '#00ffff';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(touchIndicator.x, touchIndicator.y, 20, 0, Math.PI * 2);
            ctx.stroke();

            // Inner circle
            ctx.globalAlpha = alpha * 0.5;
            ctx.fillStyle = '#00ffff';
            ctx.beginPath();
            ctx.arc(touchIndicator.x, touchIndicator.y, 8, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        } else if (touchIndicator.active && Date.now() >= touchIndicator.fadeTime) {
            touchIndicator.active = false;
        }
    }

    // Initialize game
    function startGame() {
        try {
            console.log('Starting Cyberpunk Pong 1077...');

            // Set initial canvas size first
            resize();

            // Initialize configuration menu
            initConfigMenu();

            initializeGameObjects();
            initParticles();
            initAudio();

            // Add event listeners for user interaction to enable audio
            document.addEventListener('click', enableAudio);
            document.addEventListener('keydown', enableAudio);
            document.addEventListener('touchstart', enableAudio);

            // Try to start background music immediately (will fail due to autoplay policy)
            setTimeout(() => {
                if (musicEnabled && audioContext) {
                    if (audioContext.state === 'running') {
                        startBackgroundMusic();
                        musicStarted = true;
                        console.log('Background music started immediately');
                    } else {
                        console.log('Audio context suspended - waiting for user interaction');
                    }
                }
            }, 1000);

            showGameStatus('🎮 CYBERPUNK PONG 1077 🎮\nClick anywhere to enable audio!\nBuild paddle levels for permanent upgrades!', 3000);
            console.log('Game initialized successfully, starting game loop...');
            gameLoop();
        } catch (error) {
            console.error('Error starting game:', error);
        }
    }

    // Handle user interaction to enable audio
    function enableAudio() {
        if (audioContext && audioContext.state === 'suspended') {
            audioContext.resume().then(() => {
                console.log('Audio context resumed successfully');
                audioInitialized = true;

                // Start background music if not already started
                if (musicEnabled && !musicStarted) {
                    setTimeout(() => {
                        startBackgroundMusic();
                        musicStarted = true;
                        console.log('Background music started after user interaction');
                    }, 500);
                }
            }).catch(error => {
                console.warn('Failed to resume audio context:', error);
            });
        }

        // Remove event listeners after first interaction
        document.removeEventListener('click', enableAudio);
        document.removeEventListener('keydown', enableAudio);
        document.removeEventListener('touchstart', enableAudio);
    }

    // Add event listeners for audio activation
    document.addEventListener('click', enableAudio);
    document.addEventListener('keydown', enableAudio);

    // Start the game when page loads
    // Ensure DOM is fully loaded before starting
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', startGame);
    } else {
        startGame();
    }
})();
</script>
</body>
</html>
